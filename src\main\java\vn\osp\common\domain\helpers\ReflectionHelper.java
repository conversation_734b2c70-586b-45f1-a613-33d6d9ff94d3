package vn.osp.common.domain.helpers;

import org.springframework.util.ReflectionUtils;

import java.lang.annotation.Annotation;
import java.lang.reflect.*;
import java.util.*;

public final class ReflectionHelper {
    private static final Map<Class<?>, Class<?>> WRAPPER_TO_PRIMITIVE = new HashMap<>();

    static {
        WRAPPER_TO_PRIMITIVE.put(Boolean.class, boolean.class);
        WRAPPER_TO_PRIMITIVE.put(Byte.class, byte.class);
        WRAPPER_TO_PRIMITIVE.put(Character.class, char.class);
        WRAPPER_TO_PRIMITIVE.put(Short.class, short.class);
        WRAPPER_TO_PRIMITIVE.put(Integer.class, int.class);
        WRAPPER_TO_PRIMITIVE.put(Long.class, long.class);
        WRAPPER_TO_PRIMITIVE.put(Float.class, float.class);
        WRAPPER_TO_PRIMITIVE.put(Double.class, double.class);
    }

    private ReflectionHelper() {
        throw new UnsupportedOperationException("Utility class");
    }

    // ===== Class & Type =====

    /**
     * Lấy Class object từ tên class đầy đủ.
     *
     * @param className tên đầy đủ của class (bao gồm package)
     * @return Class object tương ứng
     * @throws ReflectionException nếu không tìm thấy class
     */
    public static Class<?> getClass(String className) {
        try {
            return Class.forName(className);
        } catch (ClassNotFoundException e) {
            throw new ReflectionException("Cannot load class: " + className, e);
        }
    }

    /**
     * Lấy tất cả các field từ một class, bao gồm cả field kế thừa.
     *
     * @param clazz class cần lấy field
     * @return danh sách tất cả các field
     */
    public static List<Field> getAllFields(Class<?> clazz) {
        List<Field> fields = new ArrayList<>();
        ReflectionUtils.doWithFields(clazz, fields::add);
        return fields;
    }

    /**
     * Lấy tất cả các method từ một class, bao gồm cả method kế thừa.
     *
     * @param clazz class cần lấy method
     * @return danh sách tất cả các method
     */
    public static List<Method> getAllMethods(Class<?> clazz) {
        List<Method> methods = new ArrayList<>();
        ReflectionUtils.doWithMethods(clazz, methods::add);
        return methods;
    }

    /**
     * Kiểm tra xem class có kế thừa từ superclass cụ thể hay không.
     *
     * @param clazz      class cần kiểm tra
     * @param superclass class cha cần kiểm tra
     * @return true nếu class kế thừa từ superclass, false nếu ngược lại
     */
    public static boolean isSubclassOf(Class<?> clazz, Class<?> superclass) {
        return superclass.isAssignableFrom(clazz) && !clazz.equals(superclass);
    }

    /**
     * Kiểm tra xem class có implement interface cụ thể hay không.
     *
     * @param clazz          class cần kiểm tra
     * @param interfaceClass interface cần kiểm tra
     * @return true nếu class implement interface, false nếu ngược lại
     */
    public static boolean implementsInterface(Class<?> clazz, Class<?> interfaceClass) {
        return interfaceClass.isInterface() && interfaceClass.isAssignableFrom(clazz);
    }

    /**
     * Kiểm tra xem class có kế thừa hoặc implement từ type cụ thể hay không.
     *
     * @param clazz class cần kiểm tra
     * @param type  class hoặc interface cần kiểm tra
     * @return true nếu class có quan hệ kế thừa/implement với type, false nếu ngược lại
     */
    public static boolean isAssignableFrom(Class<?> clazz, Class<?> type) {
        return type.isAssignableFrom(clazz);
    }

    // ===== Field =====

    /**
     * Lấy Field object từ tên field.
     *
     * @param clazz     class chứa field
     * @param fieldName tên của field
     * @return Field object tương ứng
     * @throws ReflectionException nếu không tìm thấy field
     */
    public static Field getField(Class<?> clazz, String fieldName) {
        Field field = ReflectionUtils.findField(clazz, fieldName);
        if (field == null) {
            throw new ReflectionException("Field not found: " + fieldName);
        }
        ReflectionUtils.makeAccessible(field);
        return field;
    }

    /**
     * Kiểm tra xem class có field với tên cho trước hay không.
     *
     * @param clazz     class cần kiểm tra
     * @param fieldName tên field cần kiểm tra
     * @return true nếu field tồn tại, false nếu ngược lại
     */
    public static boolean hasField(Class<?> clazz, String fieldName) {
        return ReflectionUtils.findField(clazz, fieldName) != null;
    }

    /**
     * Lấy giá trị của field từ object.
     *
     * @param target    object chứa field
     * @param fieldName tên field cần lấy giá trị
     * @return giá trị của field
     * @throws ReflectionException nếu không thể truy cập field
     */
    public static Object getFieldValue(Object target, String fieldName) {
        Field field = getField(target.getClass(), fieldName);
        return ReflectionUtils.getField(field, target);
    }

    /**
     * Đặt giá trị cho field của object.
     *
     * @param target    object chứa field
     * @param fieldName tên field cần đặt giá trị
     * @param value     giá trị mới cho field
     * @throws ReflectionException nếu không thể truy cập field
     */
    public static void setFieldValue(Object target, String fieldName, Object value) {
        Field field = getField(target.getClass(), fieldName);
        ReflectionUtils.setField(field, target, value);
    }

    /**
     * Lấy tất cả giá trị field từ object dưới dạng Map.
     *
     * @param target object cần lấy field values
     * @return Map với key là tên field và value là giá trị field
     */
    public static Map<String, Object> getAllFieldValues(Object target) {
        Map<String, Object> map = new LinkedHashMap<>();
        ReflectionUtils.doWithFields(target.getClass(), field -> {
            ReflectionUtils.makeAccessible(field);
            map.put(field.getName(), ReflectionUtils.getField(field, target));
        });
        return map;
    }

    // ===== Method =====

    /**
     * Lấy Method object từ tên method và các kiểu tham số.
     *
     * @param clazz          class chứa method
     * @param methodName     tên của method
     * @param parameterTypes các kiểu tham số của method
     * @return Method object tương ứng
     * @throws ReflectionException nếu không tìm thấy method
     */
    public static Method getMethod(Class<?> clazz, String methodName, Class<?>... parameterTypes) {
        Method method = ReflectionUtils.findMethod(clazz, methodName, parameterTypes);
        if (method == null) {
            throw new ReflectionException("Method not found: " + methodName);
        }
        ReflectionUtils.makeAccessible(method);
        return method;
    }

    /**
     * Kiểm tra xem class có method với tên và kiểu tham số cho trước hay không.
     *
     * @param clazz          class cần kiểm tra
     * @param methodName     tên method cần kiểm tra
     * @param parameterTypes các kiểu tham số của method
     * @return true nếu method tồn tại, false nếu ngược lại
     */
    public static boolean hasMethod(Class<?> clazz, String methodName, Class<?>... parameterTypes) {
        return ReflectionUtils.findMethod(clazz, methodName, parameterTypes) != null;
    }

    /**
     * Gọi method trên object với các tham số đã cho.
     *
     * @param target     object chứa method
     * @param methodName tên method cần gọi
     * @param args       các tham số truyền vào method
     * @return kết quả trả về từ method
     * @throws ReflectionException nếu không thể gọi method
     */
    public static Object invokeMethod(Object target, String methodName, Object... args) {
        Class<?>[] paramTypes = Arrays.stream(args).map(arg -> {
                    if (arg == null) {
                        return Object.class; // chưa biết rõ type, có thể refine sau
                    }
                    Class<?> clazz = arg.getClass();
                    return WRAPPER_TO_PRIMITIVE.getOrDefault(clazz, clazz);
                })
                .toArray(Class[]::new);

        Method method = getMethod(target.getClass(), methodName, paramTypes);
        return ReflectionUtils.invokeMethod(method, target, args);
    }

    // ===== Constructor =====

    /**
     * Tạo instance mới của class với các tham số constructor đã cho.
     *
     * @param clazz class cần tạo instance
     * @param args  các tham số truyền vào constructor
     * @return instance mới của class
     * @throws ReflectionException nếu không thể tạo instance
     */
    public static Object newInstance(Class<?> clazz, Object... args) {
        try {
            Class<?>[] paramTypes = getParamTypes(args);

            Constructor<?> constructor = clazz.getDeclaredConstructor(paramTypes);
            ReflectionUtils.makeAccessible(constructor);
            return constructor.newInstance(args);
        } catch (NoSuchMethodException | InstantiationException |
                 IllegalAccessException | InvocationTargetException e) {
            throw new ReflectionException("Cannot instantiate class: " + clazz.getName(), e);
        }
    }

    private static Class<?>[] getParamTypes(Object[] args) {
        return Arrays.stream(args).map(arg -> {
            if (arg == null) {
                return Object.class; // chưa biết rõ type, có thể refine sau
            }
            Class<?> clazz = arg.getClass();
            return WRAPPER_TO_PRIMITIVE.getOrDefault(clazz, clazz);
        }).toArray(Class[]::new);
    }

    // ===== Annotation =====

    /**
     * Kiểm tra xem element có annotation cụ thể hay không.
     *
     * @param element    element cần kiểm tra (Class, Method, Field, etc.)
     * @param annotation class của annotation cần kiểm tra
     * @return true nếu annotation tồn tại, false nếu ngược lại
     */
    public static boolean hasAnnotation(AnnotatedElement element, Class<? extends Annotation> annotation) {
        return element.isAnnotationPresent(annotation);
    }

    /**
     * Lấy annotation từ element nếu tồn tại.
     *
     * @param <A>        kiểu annotation
     * @param element    element chứa annotation
     * @param annotation class của annotation cần lấy
     * @return Optional chứa annotation nếu tồn tại, Optional.empty() nếu không
     */
    public static <A extends Annotation> Optional<A> getAnnotation(AnnotatedElement element, Class<A> annotation) {
        return Optional.ofNullable(element.getAnnotation(annotation));
    }

    // ===== Misc =====

    /**
     * Lấy kiểu generic của field.
     *
     * @param field field cần lấy kiểu generic
     * @return Class object của kiểu generic, hoặc Object.class nếu không có
     */
    public static Class<?> getGenericType(Field field) {
        Type type = field.getGenericType();
        if (type instanceof ParameterizedType parameterizedType) {
            Type[] args = parameterizedType.getActualTypeArguments();
            if (args.length > 0 && args[0] instanceof Class) {
                return (Class<?>) args[0];
            }
        }
        return Object.class;
    }

    /**
     * Sao chép properties từ source object sang target object.
     *
     * @param source object nguồn
     * @param target object đích
     */
    public static void copyProperties(Object source, Object target) {
        getAllFieldValues(source).forEach((name, value) -> {
            if (hasField(target.getClass(), name)) {
                setFieldValue(target, name, value);
            }
        });
    }

    // ===== Custom Exception =====

    /**
     * Exception tùy chỉnh cho các lỗi reflection.
     */
    public static class ReflectionException extends RuntimeException {

        /**
         * Tạo ReflectionException với message.
         *
         * @param message thông điệp lỗi
         */
        public ReflectionException(String message) {
            super(message);
        }

        /**
         * Tạo ReflectionException với message và cause.
         *
         * @param message thông điệp lỗi
         * @param cause   nguyên nhân gây lỗi
         */
        public ReflectionException(String message, Throwable cause) {
            super(message, cause);
        }
    }
}