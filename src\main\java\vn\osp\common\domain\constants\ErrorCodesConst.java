package vn.osp.common.domain.constants;

import vn.osp.common.domain.helpers.StringHelper;

import java.util.Locale;

/**
 * Static class chứa các error codes chuẩn hóa cho Domain Layer.
 * Chỉ bao gồm các business rules và domain concerns, không bao gồm authentication.
 */
public final class ErrorCodesConst {

    // Ngăn không cho khởi tạo class
    private ErrorCodesConst() {
    }

    // ==============================
    // Authorization (Business Rules Only)
    // ==============================

    /**
     * Lỗi phân quyền - user đã xác thực nhưng không có quyền theo business rules.
     * HTTP Status: 403 Forbidden
     */
    public static final String FORBIDDEN_ACCESS = "FORBIDDEN_ACCESS";

    /**
     * Quyền không đủ để thực hiện action theo business rules.
     * HTTP Status: 403 Forbidden
     */
    public static final String INSUFFICIENT_ROLE = "INSUFFICIENT_ROLE";

    /**
     * Chỉ owner mới có thể truy cập resource theo business rules.
     * HTTP Status: 403 Forbidden
     */
    public static final String OWNERSHIP_REQUIRED = "OWNERSHIP_REQUIRED";

    /**
     * Resource bị khóa tạm thời hoặc vĩnh viễn theo business rules.
     * HTTP Status: 403 Forbidden
     */
    public static final String RESOURCE_LOCKED = "RESOURCE_LOCKED";

    // ==============================
    // Data & Entity
    // ==============================

    /**
     * Entity không tồn tại trong hệ thống.
     * HTTP Status: 404 Not Found
     */
    public static final String ENTITY_NOT_FOUND = "ENTITY_NOT_FOUND";

    /**
     * Xung đột concurrency khi update data.
     * HTTP Status: 409 Conflict
     */
    public static final String CONCURRENCY_CONFLICT = "CONCURRENCY_CONFLICT";

    /**
     * Xung đột concurrency trong batch operations.
     * HTTP Status: 409 Conflict
     */
    public static final String BATCH_CONCURRENCY_CONFLICT = "BATCH_CONCURRENCY_CONFLICT";

    /**
     * Entity đã tồn tại (duplicate).
     * HTTP Status: 409 Conflict
     */
    public static final String ENTITY_ALREADY_EXISTS = "ENTITY_ALREADY_EXISTS";

    /**
     * Vi phạm unique constraint trong database.
     * HTTP Status: 409 Conflict
     */
    public static final String UNIQUE_CONSTRAINT_VIOLATION = "UNIQUE_CONSTRAINT_VIOLATION";

    /**
     * Vi phạm composite key constraint.
     * HTTP Status: 409 Conflict
     */
    public static final String COMPOSITE_KEY_VIOLATION = "COMPOSITE_KEY_VIOLATION";

    // ==============================
    // Business Rules
    // ==============================

    /**
     * Prefix cho business rule violations.
     * Actual code sẽ là: BUSINESS_RULE_{RULE_NAME}
     * HTTP Status: 422 Unprocessable Entity
     */
    public static final String BUSINESS_RULE_PREFIX = "BUSINESS_RULE_";

    /**
     * Operation không được phép trong trạng thái hiện tại.
     * HTTP Status: 422 Unprocessable Entity
     */
    public static final String INVALID_OPERATION_STATE = "INVALID_OPERATION_STATE";

    /**
     * Workflow state không hợp lệ.
     * HTTP Status: 422 Unprocessable Entity
     */
    public static final String INVALID_WORKFLOW_STATE = "INVALID_WORKFLOW_STATE";

    // ==============================
    // System
    // ==============================

    /**
     * Service đang maintenance.
     * HTTP Status: 503 Service Unavailable
     */
    public static final String SERVICE_MAINTENANCE = "SERVICE_MAINTENANCE";

    // ==============================
    // Helper Methods
    // ==============================

    /**
     * Tạo business rule error code từ rule name.
     *
     * @param ruleName tên rule
     * @return Error code dạng BUSINESS_RULE_{RULE_NAME}
     */
    public static String businessRule(String ruleName) {
        return BUSINESS_RULE_PREFIX + ruleName.toUpperCase(Locale.ROOT);
    }

    /**
     * Kiểm tra xem error code có phải là business rule không.
     *
     * @param errorCode error code cần kiểm tra
     * @return true nếu là business rule error code
     */
    public static boolean isBusinessRuleError(String errorCode) {
        return errorCode != null && errorCode.toUpperCase(Locale.ROOT).startsWith(BUSINESS_RULE_PREFIX);
    }

    /**
     * Extract rule name từ business rule error code.
     *
     * @param errorCode business rule error code
     * @return rule name hoặc null nếu không phải business rule error
     */
    public static String extractRuleName(String errorCode) {
        if (!isBusinessRuleError(errorCode)) {
            return StringHelper.EMPTY;
        }
        return errorCode.substring(BUSINESS_RULE_PREFIX.length());
    }
}