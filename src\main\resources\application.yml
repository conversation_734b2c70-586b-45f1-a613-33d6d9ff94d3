spring:
  messages:
    basename: validation_messages
    encoding: UTF-8

  web:
    locale: vi
    locale-resolver: accept-header

management:
  opentelemetry:
    resource-attributes:
      service.name: my-spring-service
      service.namespace: common
      service.version: v1.0.0
      deployment.environment: dev

  endpoints:
    web:
      exposure:
        include: health, metrics

  tracing:
    enabled: true
    propagation:
      consume: w3c
      produce: w3c
      type: w3c

    sampling:
      probability: 0.1 # Default to 10% trace. Override in application-dev.yml with 1.0 for development.
    baggage:
      enabled: true
      correlation:
        fields:
          - userId
          - sessionId
      tag-fields:
        - userId
        - sessionId

  otlp:
    logging:
      endpoint: ${OTEL_EXPORTER_OTLP_ENDPOINT:http://localhost:4317}
      transport: grpc
      compression: gzip
    tracing:
      endpoint: ${OTEL_EXPORTER_OTLP_ENDPOINT:http://localhost:4317}
      transport: grpc
      compression: gzip
    metrics:
      export:
        enabled: true
        url: ${OTEL_EXPORTER_OTLP_METRICS_URL:http://localhost:4318/v1/metrics}  # Collector endpoint
        step: 1m
        batch-size: 10000
