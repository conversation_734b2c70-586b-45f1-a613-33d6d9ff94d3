package vn.osp.common.domain.helpers;

import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;

import java.time.*;

import static org.junit.jupiter.api.Assertions.*;

@DisplayName("DateTimeHelper Tests")
class DateTimeHelperTests {

    private static final LocalDateTime TEST_DATE_TIME = LocalDateTime.of(2025, 9, 9, 14, 30, 45);
    private static final LocalDate TEST_DATE = LocalDate.of(2025, 9, 9);
    private static final String ISO_DATE_TIME_STRING = "2025-09-09T14:30:45";
    private static final String INVALID_DATE_STRING = "invalid-date";

    @Nested
    @DisplayName("FormatIso Method Tests")
    class FormatIsoTests {

        @Test
        @DisplayName("Nên format LocalDateTime thành chuỗi ISO thành công")
        void shouldFormatLocalDateTimeToIsoString() {
            // Act
            String result = DateTimeHelper.formatIso(TEST_DATE_TIME);

            // Assert
            assertEquals(ISO_DATE_TIME_STRING, result);
        }

        @Test
        @DisplayName("Nên trả về null khi LocalDateTime là null")
        void shouldReturnNullWhenLocalDateTimeIsNull() {
            // Act
            String result = DateTimeHelper.formatIso(null);

            // Assert
            assertNull(result);
        }
    }

    @Nested
    @DisplayName("ParseIso Method Tests")
    class ParseIsoTests {

        @Test
        @DisplayName("Nên parse chuỗi ISO thành LocalDateTime thành công")
        void shouldParseIsoStringToLocalDateTime() {
            // Act
            LocalDateTime result = DateTimeHelper.parseIso(ISO_DATE_TIME_STRING);

            // Assert
            assertEquals(TEST_DATE_TIME, result);
        }

        @Test
        @DisplayName("Nên trả về null khi chuỗi ISO là null")
        void shouldReturnNullWhenIsoStringIsNull() {
            // Act
            LocalDateTime result = DateTimeHelper.parseIso(null);

            // Assert
            assertNull(result);
        }

        @Test
        @DisplayName("Nên trả về null khi chuỗi ISO không hợp lệ")
        void shouldReturnNullWhenIsoStringIsInvalid() {
            // Act
            LocalDateTime result = DateTimeHelper.parseIso(INVALID_DATE_STRING);

            // Assert
            assertNull(result);
        }
    }

    @Nested
    @DisplayName("NowUtc Method Tests")
    class NowUtcTests {

        @Test
        @DisplayName("Nên trả về thời gian hiện tại theo UTC")
        void shouldReturnCurrentUtcDateTime() {
            // Arrange
            LocalDateTime before = LocalDateTime.now(ZoneOffset.UTC);

            // Act
            LocalDateTime result = DateTimeHelper.nowUtc();

            // Arrange
            LocalDateTime after = LocalDateTime.now(ZoneOffset.UTC);

            // Assert
            assertNotNull(result);
            assertTrue(result.isAfter(before.minusSeconds(1)));
            assertTrue(result.isBefore(after.plusSeconds(1)));
        }
    }

    @Nested
    @DisplayName("ToInstant Method Tests")
    class ToInstantTests {

        @Test
        @DisplayName("Nên chuyển LocalDateTime sang Instant với system timezone")
        void shouldConvertLocalDateTimeToInstant() {
            // Act
            Instant result = DateTimeHelper.toInstant(TEST_DATE_TIME);

            // Assert
            assertNotNull(result);
            assertEquals(TEST_DATE_TIME.atZone(ZoneId.systemDefault()).toInstant(), result);
        }

        @Test
        @DisplayName("Nên trả về null khi LocalDateTime là null")
        void shouldReturnNullWhenLocalDateTimeIsNull() {
            // Act
            Instant result = DateTimeHelper.toInstant((LocalDateTime) null);

            // Assert
            assertNull(result);
        }

        @Test
        @DisplayName("Nên chuyển LocalDate sang Instant tại start of day")
        void shouldConvertLocalDateToInstantAtStartOfDay() {
            // Act
            Instant result = DateTimeHelper.toInstant(TEST_DATE);

            // Assert
            assertNotNull(result);
            assertEquals(TEST_DATE.atStartOfDay(ZoneId.systemDefault()).toInstant(), result);
        }

        @Test
        @DisplayName("Nên trả về null khi LocalDate là null")
        void shouldReturnNullWhenLocalDateIsNull() {
            // Act
            Instant result = DateTimeHelper.toInstant((LocalDate) null);

            // Assert
            assertNull(result);
        }

        @Test
        @DisplayName("Nên chuyển ZonedDateTime sang Instant")
        void shouldConvertZonedDateTimeToInstant() {
            // Arrange
            ZonedDateTime zonedDateTime = TEST_DATE_TIME.atZone(ZoneId.of("UTC"));

            // Act
            Instant result = DateTimeHelper.toInstant(zonedDateTime);

            // Assert
            assertNotNull(result);
            assertEquals(zonedDateTime.toInstant(), result);
        }

        @Test
        @DisplayName("Nên trả về null khi ZonedDateTime là null")
        void shouldReturnNullWhenZonedDateTimeIsNull() {
            // Act
            Instant result = DateTimeHelper.toInstant((ZonedDateTime) null);

            // Assert
            assertNull(result);
        }

        @Test
        @DisplayName("Nên chuyển OffsetDateTime sang Instant")
        void shouldConvertOffsetDateTimeToInstant() {
            // Arrange
            OffsetDateTime offsetDateTime = TEST_DATE_TIME.atOffset(ZoneOffset.UTC);

            // Act
            Instant result = DateTimeHelper.toInstant(offsetDateTime);

            // Assert
            assertNotNull(result);
            assertEquals(offsetDateTime.toInstant(), result);
        }

        @Test
        @DisplayName("Nên trả về null khi OffsetDateTime là null")
        void shouldReturnNullWhenOffsetDateTimeIsNull() {
            // Act
            Instant result = DateTimeHelper.toInstant((OffsetDateTime) null);

            // Assert
            assertNull(result);
        }

        @Test
        @DisplayName("Nên chuyển chuỗi ISO Instant sang Instant")
        void shouldConvertIsoInstantStringToInstant() {
            // Arrange
            String instantString = "2025-09-09T14:30:45Z";

            // Act
            Instant result = DateTimeHelper.toInstant(instantString);

            // Assert
            assertNotNull(result);
            assertEquals(Instant.parse(instantString), result);
        }

        @Test
        @DisplayName("Nên chuyển chuỗi ISO LocalDateTime sang Instant")
        void shouldConvertIsoLocalDateTimeStringToInstant() {
            // Act
            Instant result = DateTimeHelper.toInstant(ISO_DATE_TIME_STRING);

            // Assert
            assertNotNull(result);
            assertEquals(TEST_DATE_TIME.atZone(ZoneId.systemDefault()).toInstant(), result);
        }

        @Test
        @DisplayName("Nên trả về null khi chuỗi datetime là null")
        void shouldReturnNullWhenDateTimeStringIsNull() {
            // Act
            Instant result = DateTimeHelper.toInstant((String) null);

            // Assert
            assertNull(result);
        }

        @Test
        @DisplayName("Nên trả về null khi chuỗi datetime rỗng")
        void shouldReturnNullWhenDateTimeStringIsEmpty() {
            // Act
            Instant result = DateTimeHelper.toInstant("");

            // Assert
            assertNull(result);
        }

        @Test
        @DisplayName("Nên trả về null khi chuỗi datetime không hợp lệ")
        void shouldReturnNullWhenDateTimeStringIsInvalid() {
            // Act
            Instant result = DateTimeHelper.toInstant(INVALID_DATE_STRING);

            // Assert
            assertNull(result);
        }
    }

    @Nested
    @DisplayName("Format Method Tests")
    class FormatTests {

        @Test
        @DisplayName("Nên format LocalDate theo pattern thành công")
        void shouldFormatLocalDateWithPattern() {
            // Arrange
            String pattern = "yyyy-MM-dd";

            // Act
            String result = DateTimeHelper.format(TEST_DATE, pattern);

            // Assert
            assertEquals("2025-09-09", result);
        }

        @Test
        @DisplayName("Nên trả về null khi LocalDate là null")
        void shouldReturnNullWhenLocalDateIsNull() {
            // Act
            String result = DateTimeHelper.format((LocalDate) null, "yyyy-MM-dd");

            // Assert
            assertNull(result);
        }

        @Test
        @DisplayName("Nên trả về null khi pattern là null")
        void shouldReturnNullWhenPatternIsNull() {
            // Act
            String result = DateTimeHelper.format(TEST_DATE, null);

            // Assert
            assertNull(result);
        }

        @Test
        @DisplayName("Nên trả về null khi pattern rỗng")
        void shouldReturnNullWhenPatternIsEmpty() {
            // Act
            String result = DateTimeHelper.format(TEST_DATE, "");

            // Assert
            assertNull(result);
        }

        @Test
        @DisplayName("Nên format Instant theo pattern thành công")
        void shouldFormatInstantWithPattern() {
            // Arrange
            Instant instant = TEST_DATE_TIME.atZone(ZoneId.systemDefault()).toInstant();
            String pattern = "yyyy-MM-dd HH:mm:ss";

            // Act
            String result = DateTimeHelper.format(instant, pattern);

            // Assert
            assertNotNull(result);
            assertTrue(result.startsWith("2025-09-09"));
        }

        @Test
        @DisplayName("Nên trả về null khi Instant là null")
        void shouldReturnNullWhenInstantIsNull() {
            // Act
            String result = DateTimeHelper.format((Instant) null, "yyyy-MM-dd");

            // Assert
            assertNull(result);
        }

        @Test
        @DisplayName("Nên trả về null khi pattern không hợp lệ")
        void shouldReturnNullWhenPatternIsInvalid() {
            // Arrange
            Instant instant = TEST_DATE_TIME.atZone(ZoneId.systemDefault()).toInstant();

            // Act
            String result = DateTimeHelper.format(instant, "invalid-pattern-[[[");

            // Assert
            assertNull(result);
        }
    }

    @Nested
    @DisplayName("IsDateRangeValid Method Tests")
    class IsDateRangeValidTests {

        @Test
        @DisplayName("Nên trả về true khi khoảng Instant hợp lệ")
        void shouldReturnTrueWhenInstantRangeIsValid() {
            // Arrange
            Instant fromDate = Instant.now().plusSeconds(3600); // 1 giờ sau
            Instant toDate = fromDate.plusSeconds(3600); // 2 giờ sau

            // Act
            boolean result = DateTimeHelper.isDateRangeValid(fromDate, toDate);

            // Assert
            assertTrue(result);
        }

        @Test
        @DisplayName("Nên trả về true khi toDate là null và fromDate hợp lệ")
        void shouldReturnTrueWhenToDateIsNullAndFromDateIsValid() {
            // Arrange
            Instant fromDate = Instant.now().plusSeconds(3600);

            // Act
            boolean result = DateTimeHelper.isDateRangeValid(fromDate, null);

            // Assert
            assertTrue(result);
        }

        @Test
        @DisplayName("Nên trả về false khi fromDate là null")
        void shouldReturnFalseWhenFromDateIsNull() {
            // Act
            boolean result = DateTimeHelper.isDateRangeValid((Instant) null, Instant.now());

            // Assert
            assertFalse(result);
        }

        @Test
        @DisplayName("Nên trả về false khi fromDate trong quá khứ")
        void shouldReturnFalseWhenFromDateIsInPast() {
            // Arrange
            Instant fromDate = Instant.now().minusSeconds(3600); // 1 giờ trước

            // Act
            boolean result = DateTimeHelper.isDateRangeValid(fromDate, null);

            // Assert
            assertFalse(result);
        }

        @Test
        @DisplayName("Nên trả về false khi toDate trước fromDate")
        void shouldReturnFalseWhenToDateIsBeforeFromDate() {
            // Arrange
            Instant fromDate = Instant.now().plusSeconds(7200); // 2 giờ sau
            Instant toDate = fromDate.minusSeconds(3600); // 1 giờ sau

            // Act
            boolean result = DateTimeHelper.isDateRangeValid(fromDate, toDate);

            // Assert
            assertFalse(result);
        }

        @Test
        @DisplayName("Nên trả về true khi khoảng LocalDate hợp lệ")
        void shouldReturnTrueWhenLocalDateRangeIsValid() {
            // Arrange
            LocalDate fromDate = LocalDate.now().plusDays(1);
            LocalDate toDate = fromDate.plusDays(1);

            // Act
            boolean result = DateTimeHelper.isDateRangeValid(fromDate, toDate);

            // Assert
            assertTrue(result);
        }

        @Test
        @DisplayName("Nên trả về false khi LocalDate fromDate là null")
        void shouldReturnFalseWhenLocalDateFromDateIsNull() {
            // Act
            boolean result = DateTimeHelper.isDateRangeValid((LocalDate) null, LocalDate.now());

            // Assert
            assertFalse(result);
        }

        @Test
        @DisplayName("Nên trả về true khi khoảng LocalDate với comparison date hợp lệ")
        void shouldReturnTrueWhenLocalDateRangeWithComparisonDateIsValid() {
            // Arrange
            LocalDate cpDate = LocalDate.now();
            LocalDate fromDate = cpDate.plusDays(1);
            LocalDate toDate = fromDate.plusDays(1);

            // Act
            boolean result = DateTimeHelper.isDateRangeValid(fromDate, toDate, cpDate);

            // Assert
            assertTrue(result);
        }

        @Test
        @DisplayName("Nên trả về false khi fromDate trước comparison date")
        void shouldReturnFalseWhenFromDateIsBeforeComparisonDate() {
            // Arrange
            LocalDate cpDate = LocalDate.now();
            LocalDate fromDate = cpDate.minusDays(1);

            // Act
            boolean result = DateTimeHelper.isDateRangeValid(fromDate, null, cpDate);

            // Assert
            assertFalse(result);
        }

        @Test
        @DisplayName("Nên trả về true khi khoảng Instant với comparison date hợp lệ")
        void shouldReturnTrueWhenInstantRangeWithComparisonDateIsValid() {
            // Arrange
            Instant cpDate = Instant.now();
            Instant fromDate = cpDate.plusSeconds(3600);
            Instant toDate = fromDate.plusSeconds(3600);

            // Act
            boolean result = DateTimeHelper.isDateRangeValid(fromDate, toDate, cpDate);

            // Assert
            assertTrue(result);
        }

        @Test
        @DisplayName("Nên trả về false khi Instant fromDate trước comparison date")
        void shouldReturnFalseWhenInstantFromDateIsBeforeComparisonDate() {
            // Arrange
            Instant cpDate = Instant.now();
            Instant fromDate = cpDate.minusSeconds(3600);

            // Act
            boolean result = DateTimeHelper.isDateRangeValid(fromDate, null, cpDate);

            // Assert
            assertFalse(result);
        }
    }

    @Nested
    @DisplayName("ToVietnamLocalDateTime Method Tests")
    class ToVietnamLocalDateTimeTests {

        @Test
        @DisplayName("Nên chuyển Instant sang LocalDateTime theo timezone Việt Nam")
        void shouldConvertInstantToVietnamLocalDateTime() {
            // Arrange
            Instant instant = Instant.parse("2025-09-09T07:30:45Z"); // UTC

            // Act
            LocalDateTime result = DateTimeHelper.toVietnamLocalDateTime(instant);

            // Assert
            assertNotNull(result);
            // Việt Nam là UTC+7, nên 07:30 UTC = 14:30 VN time
            assertEquals(LocalDateTime.of(2025, 9, 9, 14, 30, 45), result);
        }
    }

    @Nested
    @DisplayName("FormatVietnameseDateTime Method Tests")
    class FormatVietnameseDateTimeTests {

        @Test
        @DisplayName("Nên format Instant thành chuỗi tiếng Việt")
        void shouldFormatInstantToVietnameseString() {
            // Arrange
            Instant instant = Instant.parse("2025-09-09T07:30:45Z"); // UTC, Thứ 3

            // Act
            String result = DateTimeHelper.formatVietnameseDateTime(instant);

            // Assert
            assertNotNull(result);
            assertTrue(result.contains("14 giờ 30 phút 45 giây"));
            assertTrue(result.contains("Thứ 3"));
            assertTrue(result.contains("ngày 9 tháng 9 năm 2025"));
        }

        @Test
        @DisplayName("Nên trả về chuỗi rỗng khi Instant là null")
        void shouldReturnEmptyStringWhenInstantIsNull() {
            // Act
            String result = DateTimeHelper.formatVietnameseDateTime(null);

            // Assert
            assertEquals("", result);
        }

        @Test
        @DisplayName("Nên format Chủ nhật đúng")
        void shouldFormatSundayCorrectly() {
            // Arrange - 2025-09-07 là Chủ nhật
            Instant instant = Instant.parse("2025-09-07T07:30:45Z");

            // Act
            String result = DateTimeHelper.formatVietnameseDateTime(instant);

            // Assert
            assertTrue(result.contains("Chủ nhật"));
        }
    }

    @Nested
    @DisplayName("GetVietnameseDayName Method Tests")
    class GetVietnameseDayNameTests {

        @Test
        @DisplayName("Nên trả về tên tiếng Việt cho Thứ 2")
        void shouldReturnVietnameseNameForMonday() {
            // Act
            String result = DateTimeHelper.getVietnameseDayName(DayOfWeek.MONDAY);

            // Assert
            assertEquals("Thứ 2", result);
        }

        @Test
        @DisplayName("Nên trả về tên tiếng Việt cho Thứ 3")
        void shouldReturnVietnameseNameForTuesday() {
            // Act
            String result = DateTimeHelper.getVietnameseDayName(DayOfWeek.TUESDAY);

            // Assert
            assertEquals("Thứ 3", result);
        }

        @Test
        @DisplayName("Nên trả về tên tiếng Việt cho Thứ 4")
        void shouldReturnVietnameseNameForWednesday() {
            // Act
            String result = DateTimeHelper.getVietnameseDayName(DayOfWeek.WEDNESDAY);

            // Assert
            assertEquals("Thứ 4", result);
        }

        @Test
        @DisplayName("Nên trả về tên tiếng Việt cho Thứ 5")
        void shouldReturnVietnameseNameForThursday() {
            // Act
            String result = DateTimeHelper.getVietnameseDayName(DayOfWeek.THURSDAY);

            // Assert
            assertEquals("Thứ 5", result);
        }

        @Test
        @DisplayName("Nên trả về tên tiếng Việt cho Thứ 6")
        void shouldReturnVietnameseNameForFriday() {
            // Act
            String result = DateTimeHelper.getVietnameseDayName(DayOfWeek.FRIDAY);

            // Assert
            assertEquals("Thứ 6", result);
        }

        @Test
        @DisplayName("Nên trả về tên tiếng Việt cho Thứ 7")
        void shouldReturnVietnameseNameForSaturday() {
            // Act
            String result = DateTimeHelper.getVietnameseDayName(DayOfWeek.SATURDAY);

            // Assert
            assertEquals("Thứ 7", result);
        }

        @Test
        @DisplayName("Nên trả về tên tiếng Việt cho Chủ nhật")
        void shouldReturnVietnameseNameForSunday() {
            // Act
            String result = DateTimeHelper.getVietnameseDayName(DayOfWeek.SUNDAY);

            // Assert
            assertEquals("Chủ nhật", result);
        }

        @Test
        @DisplayName("Nên trả về chuỗi rỗng khi DayOfWeek là null")
        void shouldReturnEmptyStringWhenDayOfWeekIsNull() {
            // Act
            String result = DateTimeHelper.getVietnameseDayName(null);

            // Assert
            assertEquals("", result);
        }
    }

    @Nested
    @DisplayName("Edge Cases Tests")
    class EdgeCasesTests {

        @Test
        @DisplayName("Nên xử lý timezone khác nhau chính xác")
        void shouldHandleDifferentTimezonesCorrectly() {
            // Arrange
            LocalDateTime localDateTime = LocalDateTime.of(2025, 12, 25, 0, 0, 0);
            ZonedDateTime utcTime = localDateTime.atZone(ZoneId.of("UTC"));
            ZonedDateTime vietnamTime = localDateTime.atZone(ZoneId.of("Asia/Ho_Chi_Minh"));

            // Act
            Instant utcInstant = DateTimeHelper.toInstant(utcTime);
            Instant vietnamInstant = DateTimeHelper.toInstant(vietnamTime);

            // Assert
            assertNotEquals(utcInstant, vietnamInstant);
            // Việt Nam UTC+7, nên cùng thời điểm local sẽ sớm hơn UTC 7 giờ khi chuyển sang Instant
            assertEquals(Duration.ofHours(-7), Duration.between(utcInstant, vietnamInstant));
        }

        @Test
        @DisplayName("Nên xử lý leap year chính xác")
        void shouldHandleLeapYearCorrectly() {
            // Arrange
            LocalDate leapYearDate = LocalDate.of(2024, 2, 29); // 2024 là năm nhuận

            // Act
            Instant result = DateTimeHelper.toInstant(leapYearDate);

            // Assert
            assertNotNull(result);
        }

        @Test
        @DisplayName("Nên xử lý daylight saving time")
        void shouldHandleDaylightSavingTime() {
            // Arrange - Kiểm tra với timezone có DST
            LocalDateTime dateTime = LocalDateTime.of(2025, 6, 15, 12, 0, 0);
            ZonedDateTime summerTime = dateTime.atZone(ZoneId.of("America/New_York"));

            // Act
            Instant result = DateTimeHelper.toInstant(summerTime);

            // Assert
            assertNotNull(result);
        }

        @Test
        @DisplayName("Nên xử lý boundary values")
        void shouldHandleBoundaryValues() {
            // Arrange
            LocalDateTime minDateTime = LocalDateTime.MIN;
            LocalDateTime maxDateTime = LocalDateTime.MAX;

            // Act & Assert
            assertDoesNotThrow(() -> DateTimeHelper.formatIso(minDateTime));
            assertDoesNotThrow(() -> DateTimeHelper.formatIso(maxDateTime));
        }

        @Test
        @DisplayName("Nên xử lý thời gian hiện tại chính xác")
        void shouldHandleCurrentTimeAccurately() {
            // Arrange
            Instant now = Instant.now();
            Instant future = now.plusSeconds(1);

            // Act
            boolean result = DateTimeHelper.isDateRangeValid(future, null);

            // Assert
            assertTrue(result);
        }
    }
}
