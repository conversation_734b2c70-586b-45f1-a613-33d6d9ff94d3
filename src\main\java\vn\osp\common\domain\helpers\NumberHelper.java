package vn.osp.common.domain.helpers;

import java.math.BigDecimal;
import java.math.BigInteger;
import java.util.concurrent.ThreadLocalRandom;

public final class NumberHelper {
    private NumberHelper() {
    }

    /**
     * Chuyển đổi String thành Integer an toàn.
     *
     * @param value      chuỗi cần chuyển đổi
     * @param defaultVal giá trị mặc định khi chuyển đổi thất bại
     * @return giá trị Integer hoặc defaultVal nếu có lỗi
     * @throws NullPointerException nếu defaultVal là null và value không hợp lệ
     */
    public static Integer toInt(String value, Integer defaultVal) {
        try {
            return value != null ? Integer.parseInt(value.trim()) : defaultVal;
        } catch (NumberFormatException e) {
            return defaultVal;
        }
    }

    /**
     * Chuyển đổi String thành Long an toàn.
     *
     * @param value      chuỗi cần chuyển đổi
     * @param defaultVal giá trị mặc định khi chuyển đổi thất bại
     * @return giá trị Long hoặc defaultVal nếu có lỗi
     * @throws NullPointerException nếu defaultVal là null và value không hợp lệ
     */
    public static Long toLong(String value, Long defaultVal) {
        try {
            return value != null ? Long.parseLong(value.trim()) : defaultVal;
        } catch (NumberFormatException e) {
            return defaultVal;
        }
    }

    /**
     * Chuyển đổi String thành BigDecimal an toàn.
     *
     * @param value      chuỗi cần chuyển đổi
     * @param defaultVal giá trị mặc định khi chuyển đổi thất bại
     * @return giá trị BigDecimal hoặc defaultVal nếu có lỗi
     * @throws NullPointerException nếu defaultVal là null và value không hợp lệ
     */
    public static BigDecimal toBigDecimal(String value, BigDecimal defaultVal) {
        try {
            return value != null ? new BigDecimal(value.trim()) : defaultVal;
        } catch (NumberFormatException e) {
            return defaultVal;
        }
    }

    /**
     * Kiểm tra số có phải là số dương hay không.
     *
     * @param number số cần kiểm tra
     * @return true nếu number > 0, false nếu number <= 0 hoặc null
     */
    public static boolean isPositive(Number number) {
        return number != null && number.doubleValue() > 0;
    }

    /**
     * Kiểm tra số có phải là số âm hay không.
     *
     * @param number số cần kiểm tra
     * @return true nếu number < 0, false nếu number >= 0 hoặc null
     */
    public static boolean isNegative(Number number) {
        return number != null && number.doubleValue() < 0;
    }

    /**
     * Kiểm tra số có phải là số không âm hay không (>= 0).
     *
     * @param number số cần kiểm tra
     * @return true nếu number >= 0, false nếu number < 0 hoặc null
     */
    public static boolean isNonNegative(Number number) {
        return number != null && number.doubleValue() >= 0;
    }

    /**
     * So sánh an toàn giữa hai số.
     *
     * @param a số thứ nhất
     * @param b số thứ hai
     * @return số âm nếu a < b, 0 nếu a = b, số dương nếu a > b
     */
    public static int safeCompare(Number a, Number b) {
        if (a == null && b == null) return 0;
        if (a == null) return -1;
        if (b == null) return 1;
        return Double.compare(a.doubleValue(), b.doubleValue());
    }

    /**
     * Tạo số nguyên ngẫu nhiên trong khoảng [min, max] (bao gồm cả min và max).
     *
     * @param min giá trị nhỏ nhất (bao gồm)
     * @param max giá trị lớn nhất (bao gồm)
     * @return số nguyên ngẫu nhiên trong khoảng [min, max]
     * @throws IllegalArgumentException nếu min > max
     */
    public static int randomInt(int min, int max) {
        if (max == Integer.MAX_VALUE) {
            return ThreadLocalRandom.current().nextInt(min, max);
        }
        return ThreadLocalRandom.current().nextInt(min, max + 1);
    }

    /**
     * Tạo số nguyên dài ngẫu nhiên trong khoảng [min, max] (bao gồm cả min và max).
     *
     * @param min giá trị nhỏ nhất (bao gồm)
     * @param max giá trị lớn nhất (bao gồm)
     * @return số nguyên dài ngẫu nhiên trong khoảng [min, max]
     * @throws IllegalArgumentException nếu min > max
     */
    public static long randomLong(long min, long max) {
        if (max == Long.MAX_VALUE) {
            return ThreadLocalRandom.current().nextLong(min, max);
        }
        return ThreadLocalRandom.current().nextLong(min, max + 1);
    }

    /**
     * Tạo số thực ngẫu nhiên trong khoảng [min, max) (không bao gồm max).
     *
     * @param min giá trị nhỏ nhất (bao gồm)
     * @param max giá trị lớn nhất (không bao gồm)
     * @return số thực ngẫu nhiên trong khoảng [min, max)
     * @throws IllegalArgumentException nếu min >= max
     */
    public static double randomDouble(double min, double max) {
        return ThreadLocalRandom.current().nextDouble(min, max);
    }

    /**
     * Tạo giá trị boolean ngẫu nhiên.
     *
     * @return true hoặc false với xác suất 50-50
     */
    public static boolean randomBoolean() {
        return ThreadLocalRandom.current().nextBoolean();
    }

    /**
     * Tạo số BigDecimal ngẫu nhiên trong khoảng [min, max) (không bao gồm max).
     *
     * @param min giá trị nhỏ nhất (bao gồm)
     * @param max giá trị lớn nhất (không bao gồm)
     * @return số BigDecimal ngẫu nhiên trong khoảng [min, max)
     * @throws IllegalArgumentException nếu min >= max
     */
    public static BigDecimal randomBigDecimal(double min, double max) {
        return BigDecimal.valueOf(randomDouble(min, max));
    }

    private static final String[] UNITS = {
            "không", "một", "hai", "ba", "bốn", "năm",
            "sáu", "bảy", "tám", "chín"
    };

    // Danh sách chuẩn hóa các đơn vị lớn
    private static final String[] LARGE_UNITS = {
            "",              // 10^0
            "nghìn",         // 10^3
            "triệu",         // 10^6
            "tỷ",            // 10^9
            "nghìn tỷ",      // 10^12
            "triệu tỷ",      // 10^15
            // Có thể bổ sung tiếp nếu cần
    };

    /**
     * Chuyển đổi số long thành văn bản tiếng Việt.
     *
     * @param number số cần chuyển đổi
     * @return chuỗi tiếng Việt biểu diễn số
     */
    public static String numberToVietnameseWords(long number) {
        return numberToVietnameseWords(BigInteger.valueOf(number));
    }

    /**
     * Chuyển đổi số BigInteger thành văn bản tiếng Việt.
     *
     * @param number số cần chuyển đổi
     * @return chuỗi tiếng Việt biểu diễn số
     */
    public static String numberToVietnameseWords(BigInteger number) {
        if (number.equals(BigInteger.ZERO)) {
            return "không";
        }

        if (number.signum() < 0) {
            return "âm " + numberToVietnameseWords(number.negate());
        }

        StringBuilder result = new StringBuilder();
        int group = 0;

        BigInteger thousand = BigInteger.valueOf(1000);

        while (number.compareTo(BigInteger.ZERO) > 0) {
            BigInteger[] divRem = number.divideAndRemainder(thousand);
            int threeDigits = divRem[1].intValue(); // luôn < 1000
            if (threeDigits != 0) {
                String groupText = readThreeDigits(threeDigits);
                if (!groupText.isEmpty()) {
                    result.insert(0, groupText + " " + getUnit(group) + " ");
                }
            }
            number = divRem[0];
            group++;
        }

        return result.toString().trim().replaceAll("\\s+", " ");
    }

    /**
     * Đọc ba chữ số thành văn bản tiếng Việt.
     *
     * @param number số cần đọc (0-999)
     * @return chuỗi tiếng Việt biểu diễn ba chữ số
     */
    private static String readThreeDigits(int number) {
        int hundred = number / 100;
        int ten = (number % 100) / 10;
        int unit = number % 10;

        StringBuilder sb = new StringBuilder();

        if (hundred > 0) {
            sb.append(UNITS[hundred]).append(" trăm");
            if (ten == 0 && unit != 0) {
                sb.append(" lẻ");
            }
        }

        if (ten > 1) {
            sb.append(" ").append(UNITS[ten]).append(" mươi");
            if (unit == 1) {
                sb.append(" mốt");
            } else if (unit == 5) {
                sb.append(" lăm");
            } else if (unit > 0) {
                sb.append(" ").append(UNITS[unit]);
            }
        } else if (ten == 1) {
            sb.append(" mười");
            if (unit == 1) {
                sb.append(" một");
            } else if (unit == 5) {
                sb.append(" lăm");
            } else if (unit > 0) {
                sb.append(" ").append(UNITS[unit]);
            }
        } else if (ten == 0 && unit > 0) {
            sb.append(" ").append(UNITS[unit]);
        }

        return sb.toString().trim();
    }

    /**
     * Lấy đơn vị tương ứng với nhóm số (nghìn, triệu, tỷ).
     *
     * @param group chỉ số nhóm (0 = đơn vị, 1 = nghìn, 2 = triệu, 3 = tỷ, ...)
     * @return chuỗi đơn vị tương ứng
     */
    private static String getUnit(int group) {
        if (group < LARGE_UNITS.length) {
            return LARGE_UNITS[group];
        }
        // fallback nếu vượt quá danh sách → tự động tạo bằng cách thêm "tỷ" nhiều lần
        int billions = (group - 3) / 3 + 1;
        return "tỷ".repeat(billions);
    }
}
