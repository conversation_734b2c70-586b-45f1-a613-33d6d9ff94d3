package vn.osp.common.domain.helpers;

import java.util.Collection;
import java.util.Map;

public final class CollectionHelper {
    
    /**
     * Private constructor để ngăn không cho khởi tạo instance của utility class
     */
    private CollectionHelper() {}

    /**
     * Kiểm tra xem collection có rỗng hay không
     * 
     * @param collection collection cần kiểm tra
     * @return true nếu collection null hoặc rỗng, false nếu ngược lại
     */
    public static boolean isEmpty(Collection<?> collection) {
        return collection == null || collection.isEmpty();
    }

    /**
     * Kiểm tra xem collection có khác rỗng hay không
     * 
     * @param collection collection cần kiểm tra
     * @return true nếu collection không null và có phần tử, false nếu ngược lại
     */
    public static boolean isNotEmpty(Collection<?> collection) {
        return !isEmpty(collection);
    }

    /**
     * Kiểm tra xem map có rỗng hay không
     * 
     * @param map map cần kiểm tra
     * @return true nếu map null hoặc rỗng, false nếu ngược lại
     */
    public static boolean isEmpty(Map<?, ?> map) {
        return map == null || map.isEmpty();
    }

    /**
     * Kiểm tra xem map có khác rỗng hay không
     * 
     * @param map map cần kiểm tra
     * @return true nếu map không null và có phần tử, false nếu ngược lại
     */
    public static boolean isNotEmpty(Map<?, ?> map) {
        return !isEmpty(map);
    }
}
