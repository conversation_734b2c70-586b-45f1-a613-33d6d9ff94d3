package vn.osp.common.domain.helpers;

import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.RepeatedTest;

import java.math.BigDecimal;
import java.math.BigInteger;

import static org.junit.jupiter.api.Assertions.*;

@DisplayName("NumberHelper Tests")
class NumberHelperTests {

    @Test
    @DisplayName("toInt - Chuyển đổi String thành Integer thành công")
    void toInt_WithValidString_ShouldReturnInteger() {
        // When & Then
        assertEquals(Integer.valueOf(123), NumberHelper.toInt("123", 0));
        assertEquals(Integer.valueOf(-456), NumberHelper.toInt("-456", 0));
        assertEquals(Integer.valueOf(789), NumberHelper.toInt("  789  ", 0));
        assertEquals(Integer.valueOf(0), NumberHelper.toInt("0", -1));
    }

    @Test
    @DisplayName("toInt - Với String null trả về giá trị mặc định")
    void toInt_WithNullString_ShouldReturnDefault() {
        // When & Then
        assertEquals(Integer.valueOf(100), NumberHelper.toInt(null, 100));
        assertEquals(Integer.valueOf(-1), NumberHelper.toInt(null, -1));
    }

    @Test
    @DisplayName("toInt - Với String không hợp lệ trả về giá trị mặc định")
    void toInt_WithInvalidString_ShouldReturnDefault() {
        // When & Then
        assertEquals(Integer.valueOf(50), NumberHelper.toInt("abc", 50));
        assertEquals(Integer.valueOf(0), NumberHelper.toInt("12.34", 0));
        assertEquals(Integer.valueOf(-1), NumberHelper.toInt("", -1));
        assertEquals(Integer.valueOf(99), NumberHelper.toInt("123abc", 99));
    }

    @Test
    @DisplayName("toLong - Chuyển đổi String thành Long thành công")
    void toLong_WithValidString_ShouldReturnLong() {
        // When & Then
        assertEquals(Long.valueOf(123456789L), NumberHelper.toLong("123456789", 0L));
        assertEquals(Long.valueOf(-987654321L), NumberHelper.toLong("-987654321", 0L));
        assertEquals(Long.valueOf(0L), NumberHelper.toLong("  0  ", -1L));
    }

    @Test
    @DisplayName("toLong - Với String null trả về giá trị mặc định")
    void toLong_WithNullString_ShouldReturnDefault() {
        // When & Then
        assertEquals(Long.valueOf(1000L), NumberHelper.toLong(null, 1000L));
    }

    @Test
    @DisplayName("toLong - Với String không hợp lệ trả về giá trị mặc định")
    void toLong_WithInvalidString_ShouldReturnDefault() {
        // When & Then
        assertEquals(Long.valueOf(500L), NumberHelper.toLong("invalid", 500L));
        assertEquals(Long.valueOf(0L), NumberHelper.toLong("12.34", 0L));
    }

    @Test
    @DisplayName("toBigDecimal - Chuyển đổi String thành BigDecimal thành công")
    void toBigDecimal_WithValidString_ShouldReturnBigDecimal() {
        // When & Then
        assertEquals(new BigDecimal("123.456"), NumberHelper.toBigDecimal("123.456", BigDecimal.ZERO));
        assertEquals(new BigDecimal("-789.123"), NumberHelper.toBigDecimal("-789.123", BigDecimal.ZERO));
        assertEquals(new BigDecimal("0"), NumberHelper.toBigDecimal("  0  ", BigDecimal.ONE));
    }

    @Test
    @DisplayName("toBigDecimal - Với String null trả về giá trị mặc định")
    void toBigDecimal_WithNullString_ShouldReturnDefault() {
        // Given
        BigDecimal defaultValue = new BigDecimal("99.99");

        // When & Then
        assertEquals(defaultValue, NumberHelper.toBigDecimal(null, defaultValue));
    }

    @Test
    @DisplayName("toBigDecimal - Với String không hợp lệ trả về giá trị mặc định")
    void toBigDecimal_WithInvalidString_ShouldReturnDefault() {
        // Given
        BigDecimal defaultValue = new BigDecimal("10.5");

        // When & Then
        assertEquals(defaultValue, NumberHelper.toBigDecimal("abc", defaultValue));
        assertEquals(defaultValue, NumberHelper.toBigDecimal("12.34.56", defaultValue));
    }

    @Test
    @DisplayName("isPositive - Kiểm tra số dương")
    void isPositive_ShouldReturnCorrectResult() {
        // When & Then
        assertTrue(NumberHelper.isPositive(1));
        assertTrue(NumberHelper.isPositive(0.001));
        assertTrue(NumberHelper.isPositive(Long.MAX_VALUE));
        assertTrue(NumberHelper.isPositive(new BigDecimal("0.1")));

        assertFalse(NumberHelper.isPositive(0));
        assertFalse(NumberHelper.isPositive(-1));
        assertFalse(NumberHelper.isPositive(-0.001));
        assertFalse(NumberHelper.isPositive(null));
    }

    @Test
    @DisplayName("isNegative - Kiểm tra số âm")
    void isNegative_ShouldReturnCorrectResult() {
        // When & Then
        assertTrue(NumberHelper.isNegative(-1));
        assertTrue(NumberHelper.isNegative(-0.001));
        assertTrue(NumberHelper.isNegative(Long.MIN_VALUE));
        assertTrue(NumberHelper.isNegative(new BigDecimal("-0.1")));

        assertFalse(NumberHelper.isNegative(0));
        assertFalse(NumberHelper.isNegative(1));
        assertFalse(NumberHelper.isNegative(0.001));
        assertFalse(NumberHelper.isNegative(null));
    }

    @Test
    @DisplayName("isNonNegative - Kiểm tra số không âm")
    void isNonNegative_ShouldReturnCorrectResult() {
        // When & Then
        assertTrue(NumberHelper.isNonNegative(0));
        assertTrue(NumberHelper.isNonNegative(1));
        assertTrue(NumberHelper.isNonNegative(0.001));
        assertTrue(NumberHelper.isNonNegative(Long.MAX_VALUE));
        assertTrue(NumberHelper.isNonNegative(new BigDecimal("0.1")));

        assertFalse(NumberHelper.isNonNegative(-1));
        assertFalse(NumberHelper.isNonNegative(-0.001));
        assertFalse(NumberHelper.isNonNegative(null));
    }

    @Test
    @DisplayName("safeCompare - So sánh an toàn giữa hai số")
    void safeCompare_ShouldReturnCorrectComparison() {
        // When & Then
        assertEquals(0, NumberHelper.safeCompare(null, null));
        assertEquals(-1, NumberHelper.safeCompare(null, 5));
        assertEquals(1, NumberHelper.safeCompare(5, null));
        
        assertEquals(0, NumberHelper.safeCompare(10, 10));
        assertTrue(NumberHelper.safeCompare(5, 10) < 0);
        assertTrue(NumberHelper.safeCompare(10, 5) > 0);
        
        assertEquals(0, NumberHelper.safeCompare(0.0, 0));
        assertTrue(NumberHelper.safeCompare(-5.5, 2.3) < 0);
        assertTrue(NumberHelper.safeCompare(100L, 50) > 0);
    }

    @Test
    @DisplayName("randomInt - Tạo số nguyên ngẫu nhiên trong khoảng")
    void randomInt_ShouldReturnNumberInRange() {
        // Given
        int min = 1;
        int max = 10;

        // When & Then
        for (int i = 0; i < 100; i++) {
            int random = NumberHelper.randomInt(min, max);
            assertTrue(random >= min && random <= max, 
                "Random number " + random + " should be between " + min + " and " + max);
        }
    }

    @Test
    @DisplayName("randomInt - Với min = max trả về chính số đó")
    void randomInt_WithEqualMinMax_ShouldReturnThatNumber() {
        // When & Then
        assertEquals(5, NumberHelper.randomInt(5, 5));
        assertEquals(-3, NumberHelper.randomInt(-3, -3));
    }

    @Test
    @DisplayName("randomLong - Tạo số nguyên dài ngẫu nhiên trong khoảng")
    void randomLong_ShouldReturnNumberInRange() {
        // Given
        long min = 1000L;
        long max = 2000L;

        // When & Then
        for (int i = 0; i < 100; i++) {
            long random = NumberHelper.randomLong(min, max);
            assertTrue(random >= min && random <= max,
                "Random number " + random + " should be between " + min + " and " + max);
        }
    }

    @Test
    @DisplayName("randomDouble - Tạo số thực ngẫu nhiên trong khoảng")
    void randomDouble_ShouldReturnNumberInRange() {
        // Given
        double min = 1.5;
        double max = 5.5;

        // When & Then
        for (int i = 0; i < 100; i++) {
            double random = NumberHelper.randomDouble(min, max);
            assertTrue(random >= min && random < max,
                "Random number " + random + " should be between " + min + " and " + max);
        }
    }

    @RepeatedTest(10)
    @DisplayName("randomBoolean - Tạo giá trị boolean ngẫu nhiên")
    void randomBoolean_ShouldReturnBooleanValue() {
        // When
        boolean random = NumberHelper.randomBoolean();

        // Then
        assertTrue(random == true || random == false);
    }

    @Test
    @DisplayName("randomBigDecimal - Tạo BigDecimal ngẫu nhiên trong khoảng")
    void randomBigDecimal_ShouldReturnNumberInRange() {
        // Given
        double min = 10.5;
        double max = 20.5;

        // When & Then
        for (int i = 0; i < 100; i++) {
            BigDecimal random = NumberHelper.randomBigDecimal(min, max);
            assertTrue(random.doubleValue() >= min && random.doubleValue() < max,
                "Random number " + random + " should be between " + min + " and " + max);
        }
    }

    @Test
    @DisplayName("numberToVietnameseWords với long - Chuyển đổi số thành chữ tiếng Việt")
    void numberToVietnameseWords_WithLong_ShouldReturnVietnameseText() {
        // When & Then
        assertEquals("không", NumberHelper.numberToVietnameseWords(0L));
        assertEquals("một", NumberHelper.numberToVietnameseWords(1L));
        assertEquals("mười", NumberHelper.numberToVietnameseWords(10L));
        assertEquals("mười một", NumberHelper.numberToVietnameseWords(11L));
        assertEquals("mười lăm", NumberHelper.numberToVietnameseWords(15L));
        assertEquals("hai mươi", NumberHelper.numberToVietnameseWords(20L));
        assertEquals("hai mươi mốt", NumberHelper.numberToVietnameseWords(21L));
        assertEquals("hai mươi lăm", NumberHelper.numberToVietnameseWords(25L));
        assertEquals("một trăm", NumberHelper.numberToVietnameseWords(100L));
        assertEquals("một trăm lẻ một", NumberHelper.numberToVietnameseWords(101L));
        assertEquals("một trăm mười", NumberHelper.numberToVietnameseWords(110L));
        assertEquals("một trăm hai mươi ba", NumberHelper.numberToVietnameseWords(123L));
        assertEquals("một nghìn", NumberHelper.numberToVietnameseWords(1000L));
        assertEquals("một nghìn hai trăm ba mươi bốn", NumberHelper.numberToVietnameseWords(1234L));
        assertEquals("một triệu", NumberHelper.numberToVietnameseWords(1000000L));
        assertEquals("một tỷ", NumberHelper.numberToVietnameseWords(1000000000L));
    }

    @Test
    @DisplayName("numberToVietnameseWords với số âm - Xử lý số âm")
    void numberToVietnameseWords_WithNegativeNumber_ShouldReturnNegativeText() {
        // When & Then
        assertEquals("âm một", NumberHelper.numberToVietnameseWords(-1L));
        assertEquals("âm mười", NumberHelper.numberToVietnameseWords(-10L));
        assertEquals("âm một trăm", NumberHelper.numberToVietnameseWords(-100L));
        assertEquals("âm một nghìn", NumberHelper.numberToVietnameseWords(-1000L));
    }

    @Test
    @DisplayName("numberToVietnameseWords với BigInteger - Chuyển đổi số lớn")
    void numberToVietnameseWords_WithBigInteger_ShouldReturnVietnameseText() {
        // When & Then
        assertEquals("không", NumberHelper.numberToVietnameseWords(BigInteger.ZERO));
        assertEquals("một", NumberHelper.numberToVietnameseWords(BigInteger.ONE));
        assertEquals("âm một", NumberHelper.numberToVietnameseWords(BigInteger.valueOf(-1)));
        
        BigInteger bigNumber = new BigInteger("123456789");
        String result = NumberHelper.numberToVietnameseWords(bigNumber);
        assertNotNull(result);
        assertTrue(result.contains("triệu"));
        
        BigInteger veryBigNumber = new BigInteger("1000000000000"); // 1 nghìn tỷ
        String bigResult = NumberHelper.numberToVietnameseWords(veryBigNumber);
        assertNotNull(bigResult);
        assertTrue(bigResult.contains("nghìn tỷ"));
    }

    @Test
    @DisplayName("numberToVietnameseWords - Test các trường hợp đặc biệt")
    void numberToVietnameseWords_SpecialCases_ShouldReturnCorrectText() {
        // When & Then
        assertEquals("năm", NumberHelper.numberToVietnameseWords(5L));
        assertEquals("mười lăm", NumberHelper.numberToVietnameseWords(15L));
        assertEquals("hai mươi lăm", NumberHelper.numberToVietnameseWords(25L));
        assertEquals("ba mươi lăm", NumberHelper.numberToVietnameseWords(35L));
        
        assertEquals("hai mươi mốt", NumberHelper.numberToVietnameseWords(21L));
        assertEquals("ba mươi mốt", NumberHelper.numberToVietnameseWords(31L));
        assertEquals("bốn mươi mốt", NumberHelper.numberToVietnameseWords(41L));
        
        assertEquals("một trăm lẻ một", NumberHelper.numberToVietnameseWords(101L));
        assertEquals("một trăm lẻ năm", NumberHelper.numberToVietnameseWords(105L));
        assertEquals("hai trăm lẻ hai", NumberHelper.numberToVietnameseWords(202L));
    }

    @Test
    @DisplayName("numberToVietnameseWords - Test số có nhiều chữ số")
    void numberToVietnameseWords_WithLargeNumbers_ShouldReturnCorrectText() {
        // When & Then
        assertEquals("chín trăm chín mươi chín", NumberHelper.numberToVietnameseWords(999L));
        assertEquals("chín nghìn chín trăm chín mươi chín", NumberHelper.numberToVietnameseWords(9999L));
        assertEquals("chín mươi chín nghìn chín trăm chín mươi chín", NumberHelper.numberToVietnameseWords(99999L));
        assertEquals("chín trăm chín mươi chín nghìn chín trăm chín mươi chín", NumberHelper.numberToVietnameseWords(999999L));
        
        // Test với triệu
        assertEquals("một triệu hai trăm ba mươi bốn nghìn năm trăm sáu mươi bảy", 
            NumberHelper.numberToVietnameseWords(1234567L));
            
        // Test với tỷ
        assertEquals("hai tỷ ba trăm bốn mươi lăm triệu sáu trăm bảy mươi tám nghìn chín trăm", 
            NumberHelper.numberToVietnameseWords(2345678900L));
    }

    @Test
    @DisplayName("Integration test - Test toàn bộ quy trình với các loại số khác nhau")
    void integrationTest_WithVariousNumberTypes_ShouldWorkCorrectly() {
        // String conversion tests
        assertNotNull(NumberHelper.toInt("42", 0));
        assertNotNull(NumberHelper.toLong("123456", 0L));
        assertNotNull(NumberHelper.toBigDecimal("123.45", BigDecimal.ZERO));
        
        // Number validation tests
        assertTrue(NumberHelper.isPositive(42));
        assertTrue(NumberHelper.isNegative(-42));
        assertTrue(NumberHelper.isNonNegative(0));
        
        // Comparison test
        assertEquals(0, NumberHelper.safeCompare(42, 42));
        
        // Random number generation tests
        int randomInt = NumberHelper.randomInt(1, 100);
        assertTrue(randomInt >= 1 && randomInt <= 100);
        
        // Vietnamese words conversion test
        String vietnameseText = NumberHelper.numberToVietnameseWords(randomInt);
        assertNotNull(vietnameseText);
        assertFalse(vietnameseText.isEmpty());
    }
}