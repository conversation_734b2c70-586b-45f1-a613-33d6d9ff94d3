package vn.osp.common.infrastructure.listeners;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.hibernate.event.spi.PreDeleteEvent;
import org.hibernate.event.spi.PreDeleteEventListener;
import org.springframework.stereotype.Component;
import vn.osp.common.domain.domainentitytypes.SoftDelete;
import vn.osp.common.domain.domainentitytypes.SoftDeleteUuid;
import vn.osp.common.domain.helpers.UuidHelper;

import java.time.LocalDateTime;
import java.util.UUID;

/**
 * Hibernate Event Listener thực hiện soft delete thay vì hard delete
 * cho các entity implement SoftDelete interface.
 */
@Component
@RequiredArgsConstructor
@Slf4j
public class SoftDeleteEventListener implements PreDeleteEventListener {

    private final DomainContextProvider contextProvider;

    @Override
    public boolean onPreDelete(PreDeleteEvent event) {
        Object entity = event.getEntity();

        if (entity instanceof SoftDelete<?>) {
            performSoftDelete((SoftDelete<?>) entity, event);
            return true; // Chặn hard delete
        }

        return false; // Cho phép hard delete bình thường
    }

    private void performSoftDelete(SoftDelete<?> entity, PreDeleteEvent event) {
        LocalDateTime now = LocalDateTime.now();
        UUID currentUserId = contextProvider.getCurrentUserId();

        // Set soft delete fields
        entity.setDeleted(true);
        entity.setDeletedAt(now);

        if (UuidHelper.isNotEmpty(currentUserId) && entity instanceof SoftDeleteUuid deletable) {
            deletable.setDeletedBy(currentUserId);
        }

        log.debug("Performed soft delete for entity: {} - DeletedAt: {}, DeletedBy: {}",
                entity.getClass().getSimpleName(), now, currentUserId);

        // Thực hiện UPDATE thay vì DELETE
        event.getSession().merge(entity);
        event.getSession().flush();
    }
}
