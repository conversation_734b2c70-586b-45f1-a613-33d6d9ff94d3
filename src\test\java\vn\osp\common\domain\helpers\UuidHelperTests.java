package vn.osp.common.domain.helpers;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.ValueSource;

import java.util.UUID;

import static org.junit.jupiter.api.Assertions.*;

@DisplayName("Kiểm thử UuidHelper")
class UuidHelperTests {

    @Test
    @DisplayName("isEmpty - trả về true cho UUID null")
    void isEmpty_shouldReturnTrueForNullUuid() {
        assertTrue(UuidHelper.isEmpty(null));
    }

    @Test
    @DisplayName("isEmpty - trả về true cho UUID rỗng")
    void isEmpty_shouldReturnTrueForEmptyUuid() {
        assertTrue(UuidHelper.isEmpty(UuidHelper.EMPTY));
        assertTrue(UuidHelper.isEmpty(new UUID(0L, 0L)));
    }

    @Test
    @DisplayName("isEmpty - trả về false cho UUID hợp lệ")
    void isEmpty_shouldReturnFalseForValidUuid() {
        UUID validUuid = UUID.randomUUID();
        assertFalse(UuidHelper.isEmpty(validUuid));
        
        UUID specificUuid = UUID.fromString("550e8400-e29b-41d4-a716-************");
        assertFalse(UuidHelper.isEmpty(specificUuid));
    }

    @Test
    @DisplayName("random - tạo UUID ngẫu nhiên hợp lệ")
    void random_shouldGenerateValidRandomUuid() {
        UUID uuid1 = UuidHelper.random();
        UUID uuid2 = UuidHelper.random();
        
        // Kiểm tra UUID được tạo không null
        assertNotNull(uuid1);
        assertNotNull(uuid2);
        
        // Kiểm tra mỗi lần gọi tạo UUID khác nhau
        assertNotEquals(uuid1, uuid2);
        
        // Kiểm tra UUID không rỗng
        assertFalse(UuidHelper.isEmpty(uuid1));
        assertFalse(UuidHelper.isEmpty(uuid2));
    }

    @Test
    @DisplayName("fromString - chuyển đổi chuỗi UUID hợp lệ")
    void fromString_shouldConvertValidUuidString() {
        String validUuidString = "550e8400-e29b-41d4-a716-************";
        UUID expectedUuid = UUID.fromString(validUuidString);
        
        UUID result = UuidHelper.fromString(validUuidString);
        
        assertEquals(expectedUuid, result);
        assertFalse(UuidHelper.isEmpty(result));
    }

    @Test
    @DisplayName("fromString - trả về EMPTY cho input null")
    void fromString_shouldReturnEmptyForNullInput() {
        UUID result = UuidHelper.fromString(null);
        
        assertEquals(UuidHelper.EMPTY, result);
        assertTrue(UuidHelper.isEmpty(result));
    }

    @ParameterizedTest
    @ValueSource(strings = {
        "",
        "   ",
        "invalid-uuid",
        "550e8400-e29b-41d4-a716",
        "550e8400-e29b-41d4-a716-************-extra",
        "ggg8400-e29b-41d4-a716-************",
        "550e8400-e29b-41d4-a716-44665544000z",
        "not-a-uuid-at-all"
    })
    @DisplayName("fromString - trả về EMPTY cho chuỗi không hợp lệ")
    void fromString_shouldReturnEmptyForInvalidStrings(String invalidUuid) {
        UUID result = UuidHelper.fromString(invalidUuid);
        
        assertEquals(UuidHelper.EMPTY, result);
        assertTrue(UuidHelper.isEmpty(result));
    }

    @Test
    @DisplayName("fromString - xử lý các format UUID khác nhau")
    void fromString_shouldHandleDifferentUuidFormats() {
        // UUID với chữ hoa
        String upperCaseUuid = "550E8400-E29B-41D4-A716-************";
        UUID result1 = UuidHelper.fromString(upperCaseUuid);
        assertFalse(UuidHelper.isEmpty(result1));
        
        // UUID với chữ thường
        String lowerCaseUuid = "550e8400-e29b-41d4-a716-************";
        UUID result2 = UuidHelper.fromString(lowerCaseUuid);
        assertFalse(UuidHelper.isEmpty(result2));
        
        // Kiểm tra cả hai UUID tương đương
        assertEquals(result1, result2);
    }

    @Test
    @DisplayName("fromString - không ném exception cho input không hợp lệ")
    void fromString_shouldNotThrowExceptionForInvalidInput() {
        // Đảm bảo method không ném exception với bất kỳ input nào
        assertDoesNotThrow(() -> {
            UuidHelper.fromString("completely-invalid-input");
            UuidHelper.fromString("");
            UuidHelper.fromString("123");
            UuidHelper.fromString("@#$%^&*()");
        });
    }

    @Test
    @DisplayName("hằng số EMPTY - phải là UUID với giá trị 00000000-0000-0000-0000-000000000000")
    void emptyConstant_shouldBeZeroUuid() {
        assertEquals(new UUID(0L, 0L), UuidHelper.EMPTY);
        assertEquals("00000000-0000-0000-0000-000000000000", UuidHelper.EMPTY.toString());
        assertTrue(UuidHelper.isEmpty(UuidHelper.EMPTY));
    }

    @Test
    @DisplayName("tích hợp - kiểm tra tương tác giữa các method")
    void integration_shouldWorkTogetherCorrectly() {
        // Tạo UUID ngẫu nhiên
        UUID randomUuid = UuidHelper.random();
        
        // Chuyển thành chuỗi và chuyển lại thành UUID
        String uuidString = randomUuid.toString();
        UUID convertedUuid = UuidHelper.fromString(uuidString);
        
        // Kiểm tra kết quả
        assertEquals(randomUuid, convertedUuid);
        assertFalse(UuidHelper.isEmpty(convertedUuid));
        
        // Kiểm tra với UUID rỗng
        String emptyUuidString = UuidHelper.EMPTY.toString();
        UUID convertedEmptyUuid = UuidHelper.fromString(emptyUuidString);
        
        assertEquals(UuidHelper.EMPTY, convertedEmptyUuid);
        assertTrue(UuidHelper.isEmpty(convertedEmptyUuid));
    }
}