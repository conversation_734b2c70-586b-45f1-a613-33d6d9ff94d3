package vn.osp.common.domain.helpers;

import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;

import java.nio.ByteBuffer;
import java.nio.charset.Charset;
import java.nio.charset.StandardCharsets;

import static org.junit.jupiter.api.Assertions.*;

@DisplayName("Kiểm thử EncodingHelper")
class EncodingHelperTests {

    @Nested
    @DisplayName("Kiểm thử Base64 Encoding/Decoding")
    class Base64Tests {

        @Test
        @DisplayName("Nên encode mảng byte thành chuỗi Base64")
        void shouldEncodeByteArrayToBase64() {
            // Chuẩn bị dữ liệu
            byte[] data = "Hello World".getBytes(StandardCharsets.UTF_8);
            
            // Thực thi
            String result = EncodingHelper.base64Encode(data);
            
            // Xác minh kết quả
            assertNotNull(result);
            assertEquals("SGVsbG8gV29ybGQ=", result);
        }

        @Test
        @DisplayName("Nên trả về chuỗi rỗng khi encode mảng byte null")
        void shouldReturnEmptyStringWhenEncodingNullByteArray() {
            // Thực thi
            String result = EncodingHelper.base64Encode((byte[]) null);
            
            // Xác minh kết quả
            assertEquals("", result);
        }

        @Test
        @DisplayName("Nên trả về chuỗi rỗng khi encode mảng byte trống")
        void shouldReturnEmptyStringWhenEncodingEmptyByteArray() {
            // Chuẩn bị dữ liệu
            byte[] data = new byte[0];
            
            // Thực thi
            String result = EncodingHelper.base64Encode(data);
            
            // Xác minh kết quả
            assertEquals("", result);
        }

        @Test
        @DisplayName("Nên decode chuỗi Base64 thành mảng byte")
        void shouldDecodeBase64StringToByteArray() {
            // Chuẩn bị dữ liệu
            String base64 = "SGVsbG8gV29ybGQ=";
            
            // Thực thi
            byte[] result = EncodingHelper.base64Decode(base64);
            
            // Xác minh kết quả
            assertNotNull(result);
            assertArrayEquals("Hello World".getBytes(StandardCharsets.UTF_8), result);
        }

        @Test
        @DisplayName("Nên trả về mảng byte rỗng khi decode chuỗi null")
        void shouldReturnEmptyByteArrayWhenDecodingNullString() {
            // Thực thi
            byte[] result = EncodingHelper.base64Decode((String) null);
            
            // Xác minh kết quả
            assertNotNull(result);
            assertEquals(0, result.length);
        }

        @Test
        @DisplayName("Nên trả về mảng byte rỗng khi decode chuỗi rỗng")
        void shouldReturnEmptyByteArrayWhenDecodingEmptyString() {
            // Thực thi
            byte[] result = EncodingHelper.base64Decode("");
            
            // Xác minh kết quả
            assertNotNull(result);
            assertEquals(0, result.length);
        }

        @Test
        @DisplayName("Nên ném exception cho chuỗi Base64 không hợp lệ")
        void shouldThrowExceptionForInvalidBase64String() {
            // Chuẩn bị dữ liệu
            String invalidBase64 = "Invalid Base64!@#";
            
            // Thực thi & Xác minh kết quả
            assertThrows(IllegalArgumentException.class, () -> 
                EncodingHelper.base64Decode(invalidBase64));
        }

        @Test
        @DisplayName("Nên encode văn bản thành chuỗi Base64")
        void shouldEncodeTextToBase64() {
            // Chuẩn bị dữ liệu
            String text = "Hello World";
            
            // Thực thi
            String result = EncodingHelper.base64Encode(text);
            
            // Xác minh kết quả
            assertNotNull(result);
            assertEquals("SGVsbG8gV29ybGQ=", result);
        }

        @Test
        @DisplayName("Nên trả về chuỗi rỗng khi encode văn bản null")
        void shouldReturnEmptyStringWhenEncodingNullText() {
            // Thực thi
            String result = EncodingHelper.base64Encode((String) null);
            
            // Xác minh kết quả
            assertEquals("", result);
        }

        @Test
        @DisplayName("Nên decode chuỗi Base64 thành văn bản")
        void shouldDecodeBase64StringToText() {
            // Chuẩn bị dữ liệu
            String base64 = "SGVsbG8gV29ybGQ=";
            
            // Thực thi
            String result = EncodingHelper.base64DecodeToString(base64);
            
            // Xác minh kết quả
            assertNotNull(result);
            assertEquals("Hello World", result);
        }

        @Test
        @DisplayName("Nên encode mảng byte thành Base64 URL-safe")
        void shouldEncodeByteArrayToUrlSafeBase64() {
            // Chuẩn bị dữ liệu
            byte[] data = "Hello>World?".getBytes(StandardCharsets.UTF_8);
            
            // Thực thi
            String result = EncodingHelper.base64UrlEncode(data);
            
            // Xác minh kết quả
            assertNotNull(result);
            assertFalse(result.contains("+"));
            assertFalse(result.contains("/"));
            assertFalse(result.contains("="));
        }

        @Test
        @DisplayName("Nên decode Base64 URL-safe thành mảng byte")
        void shouldDecodeUrlSafeBase64ToByteArray() {
            // Chuẩn bị dữ liệu
            String urlSafeBase64 = EncodingHelper.base64UrlEncode("Hello>World?".getBytes(StandardCharsets.UTF_8));
            
            // Thực thi
            byte[] result = EncodingHelper.base64UrlDecode(urlSafeBase64);
            
            // Xác minh kết quả
            assertNotNull(result);
            assertArrayEquals("Hello>World?".getBytes(StandardCharsets.UTF_8), result);
        }

        @Test
        @DisplayName("Nên encode mảng byte thành Base64 MIME")
        void shouldEncodeByteArrayToMimeBase64() {
            // Chuẩn bị dữ liệu
            byte[] data = "A".repeat(100).getBytes(StandardCharsets.UTF_8);
            
            // Thực thi
            String result = EncodingHelper.base64MimeEncode(data);
            
            // Xác minh kết quả
            assertNotNull(result);
            assertTrue(result.contains("\r\n")); // Định dạng MIME bao gồm line breaks
        }

        @Test
        @DisplayName("Nên decode Base64 MIME thành mảng byte")
        void shouldDecodeMimeBase64ToByteArray() {
            // Chuẩn bị dữ liệu
            byte[] originalData = "A".repeat(100).getBytes(StandardCharsets.UTF_8);
            String mimeBase64 = EncodingHelper.base64MimeEncode(originalData);
            
            // Thực thi
            byte[] result = EncodingHelper.base64MimeDecode(mimeBase64);
            
            // Xác minh kết quả
            assertNotNull(result);
            assertArrayEquals(originalData, result);
        }
    }

    @Nested
    @DisplayName("Kiểm thử Hexadecimal Encoding/Decoding")
    class HexTests {

        @Test
        @DisplayName("Nên encode mảng byte thành chuỗi hex")
        void shouldEncodeByteArrayToHex() {
            // Chuẩn bị dữ liệu
            byte[] data = {0x01, 0x23, 0x45, 0x67, (byte) 0x89, (byte) 0xAB, (byte) 0xCD, (byte) 0xEF};
            
            // Thực thi
            String result = EncodingHelper.hexEncode(data);
            
            // Xác minh kết quả
            assertNotNull(result);
            assertEquals("0123456789abcdef", result);
        }

        @Test
        @DisplayName("Nên trả về chuỗi rỗng khi encode mảng byte null thành hex")
        void shouldReturnEmptyStringWhenEncodingNullByteArrayToHex() {
            // Thực thi
            String result = EncodingHelper.hexEncode((byte[]) null);
            
            // Xác minh kết quả
            assertEquals("", result);
        }

        @Test
        @DisplayName("Nên trả về chuỗi rỗng khi encode mảng byte trống thành hex")
        void shouldReturnEmptyStringWhenEncodingEmptyByteArrayToHex() {
            // Chuẩn bị dữ liệu
            byte[] data = new byte[0];
            
            // Thực thi
            String result = EncodingHelper.hexEncode(data);
            
            // Xác minh kết quả
            assertEquals("", result);
        }

        @Test
        @DisplayName("Nên decode chuỗi hex thành mảng byte")
        void shouldDecodeHexStringToByteArray() {
            // Chuẩn bị dữ liệu
            String hex = "0123456789abcdef";
            
            // Thực thi
            byte[] result = EncodingHelper.hexDecode(hex);
            
            // Xác minh kết quả
            assertNotNull(result);
            byte[] expected = {0x01, 0x23, 0x45, 0x67, (byte) 0x89, (byte) 0xAB, (byte) 0xCD, (byte) 0xEF};
            assertArrayEquals(expected, result);
        }

        @Test
        @DisplayName("Nên decode chuỗi hex viết hoa thành mảng byte")
        void shouldDecodeUppercaseHexStringToByteArray() {
            // Chuẩn bị dữ liệu
            String hex = "0123456789ABCDEF";
            
            // Thực thi
            byte[] result = EncodingHelper.hexDecode(hex);
            
            // Xác minh kết quả
            assertNotNull(result);
            byte[] expected = {0x01, 0x23, 0x45, 0x67, (byte) 0x89, (byte) 0xAB, (byte) 0xCD, (byte) 0xEF};
            assertArrayEquals(expected, result);
        }

        @Test
        @DisplayName("Nên trả về mảng byte rỗng khi decode chuỗi hex null")
        void shouldReturnEmptyByteArrayWhenDecodingNullHexString() {
            // Thực thi
            byte[] result = EncodingHelper.hexDecode(null);
            
            // Xác minh kết quả
            assertNotNull(result);
            assertEquals(0, result.length);
        }

        @Test
        @DisplayName("Nên trả về mảng byte rỗng khi decode chuỗi hex trống")
        void shouldReturnEmptyByteArrayWhenDecodingEmptyHexString() {
            // Thực thi
            byte[] result = EncodingHelper.hexDecode("");
            
            // Xác minh kết quả
            assertNotNull(result);
            assertEquals(0, result.length);
        }

        @Test
        @DisplayName("Nên ném exception cho chuỗi hex có độ dài lẻ")
        void shouldThrowExceptionForOddLengthHexString() {
            // Chuẩn bị dữ liệu
            String oddLengthHex = "123";
            
            // Thực thi & Xác minh kết quả
            IllegalArgumentException exception = assertThrows(IllegalArgumentException.class, () -> 
                EncodingHelper.hexDecode(oddLengthHex));
            assertEquals("Invalid hex string length", exception.getMessage());
        }

        @Test
        @DisplayName("Nên ném exception cho ký tự hex không hợp lệ")
        void shouldThrowExceptionForInvalidHexCharacter() {
            // Chuẩn bị dữ liệu
            String invalidHex = "12GH";
            
            // Thực thi & Xác minh kết quả
            IllegalArgumentException exception = assertThrows(IllegalArgumentException.class, () -> 
                EncodingHelper.hexDecode(invalidHex));
            assertEquals("Invalid hex character", exception.getMessage());
        }

        @Test
        @DisplayName("Nên encode văn bản thành chuỗi hex")
        void shouldEncodeTextToHex() {
            // Chuẩn bị dữ liệu
            String text = "Hello";
            
            // Thực thi
            String result = EncodingHelper.hexEncode(text);
            
            // Xác minh kết quả
            assertNotNull(result);
            assertEquals("48656c6c6f", result);
        }

        @Test
        @DisplayName("Nên trả về chuỗi rỗng khi encode văn bản null thành hex")
        void shouldReturnEmptyStringWhenEncodingNullTextToHex() {
            // Thực thi
            String result = EncodingHelper.hexEncode((String) null);
            
            // Xác minh kết quả
            assertEquals("", result);
        }

        @Test
        @DisplayName("Nên decode chuỗi hex thành văn bản")
        void shouldDecodeHexStringToText() {
            // Chuẩn bị dữ liệu
            String hex = "48656c6c6f";
            
            // Thực thi
            String result = EncodingHelper.hexDecodeToString(hex);
            
            // Xác minh kết quả
            assertNotNull(result);
            assertEquals("Hello", result);
        }

        @Test
        @DisplayName("Nên trả về chuỗi rỗng khi decode hex null thành văn bản")
        void shouldReturnEmptyStringWhenDecodingNullHexToText() {
            // Thực thi
            String result = EncodingHelper.hexDecodeToString(null);
            
            // Xác minh kết quả
            assertEquals("", result);
        }
    }

    @Nested
    @DisplayName("Kiểm thử URL Encoding/Decoding")
    class UrlTests {

        @Test
        @DisplayName("Nên encode văn bản thành định dạng URL")
        void shouldEncodeTextToUrl() {
            // Chuẩn bị dữ liệu
            String text = "Hello World & Special Characters!";
            
            // Thực thi
            String result = EncodingHelper.urlEncode(text);
            
            // Xác minh kết quả
            assertNotNull(result);
            assertEquals("Hello+World+%26+Special+Characters%21", result);
        }

        @Test
        @DisplayName("Nên trả về chuỗi rỗng khi encode văn bản null thành URL")
        void shouldReturnEmptyStringWhenEncodingNullTextToUrl() {
            // Thực thi
            String result = EncodingHelper.urlEncode(null);
            
            // Xác minh kết quả
            assertEquals("", result);
        }

        @Test
        @DisplayName("Nên trả về chuỗi rỗng khi encode văn bản trống thành URL")
        void shouldReturnEmptyStringWhenEncodingEmptyTextToUrl() {
            // Thực thi
            String result = EncodingHelper.urlEncode("");
            
            // Xác minh kết quả
            assertEquals("", result);
        }

        @Test
        @DisplayName("Nên decode văn bản đã URL-encoded")
        void shouldDecodeUrlEncodedText() {
            // Chuẩn bị dữ liệu
            String encoded = "Hello+World+%26+Special+Characters%21";
            
            // Thực thi
            String result = EncodingHelper.urlDecode(encoded);
            
            // Xác minh kết quả
            assertNotNull(result);
            assertEquals("Hello World & Special Characters!", result);
        }

        @Test
        @DisplayName("Nên trả về chuỗi rỗng khi decode văn bản URL-encoded null")
        void shouldReturnEmptyStringWhenDecodingNullUrlEncodedText() {
            // Thực thi
            String result = EncodingHelper.urlDecode(null);
            
            // Xác minh kết quả
            assertEquals("", result);
        }

        @Test
        @DisplayName("Nên trả về chuỗi rỗng khi decode văn bản URL-encoded trống")
        void shouldReturnEmptyStringWhenDecodingEmptyUrlEncodedText() {
            // Thực thi
            String result = EncodingHelper.urlDecode("");
            
            // Xác minh kết quả
            assertEquals("", result);
        }

        @Test
        @DisplayName("Nên xử lý ký tự UTF-8 trong URL encoding")
        void shouldHandleUtf8CharactersInUrlEncoding() {
            // Chuẩn bị dữ liệu
            String text = "Tiếng Việt";
            
            // Thực thi
            String encoded = EncodingHelper.urlEncode(text);
            String decoded = EncodingHelper.urlDecode(encoded);
            
            // Xác minh kết quả
            assertNotNull(encoded);
            assertNotNull(decoded);
            assertEquals(text, decoded);
        }
    }

    @Nested
    @DisplayName("Kiểm thử HTML Encoding/Decoding")
    class HtmlTests {

        @Test
        @DisplayName("Nên encode ký tự đặc biệt HTML")
        void shouldEncodeHtmlSpecialCharacters() {
            // Chuẩn bị dữ liệu
            String text = "<script>alert('XSS');</script>";
            
            // Thực thi
            String result = EncodingHelper.htmlEncode(text);
            
            // Xác minh kết quả
            assertNotNull(result);
            assertEquals("&lt;script&gt;alert('XSS');&lt;/script&gt;", result);
        }

        @Test
        @DisplayName("Nên trả về chuỗi rỗng khi encode văn bản HTML null")
        void shouldReturnEmptyStringWhenEncodingNullHtmlText() {
            // Thực thi
            String result = EncodingHelper.htmlEncode(null);
            
            // Xác minh kết quả
            assertEquals("", result);
        }

        @Test
        @DisplayName("Nên trả về chuỗi rỗng khi encode văn bản HTML trống")
        void shouldReturnEmptyStringWhenEncodingEmptyHtmlText() {
            // Thực thi
            String result = EncodingHelper.htmlEncode("");
            
            // Xác minh kết quả
            assertEquals("", result);
        }

        @Test
        @DisplayName("Nên decode HTML entities")
        void shouldDecodeHtmlEntities() {
            // Chuẩn bị dữ liệu
            String encoded = "&lt;script&gt;alert('XSS');&lt;/script&gt;";
            
            // Thực thi
            String result = EncodingHelper.htmlDecode(encoded);
            
            // Xác minh kết quả
            assertNotNull(result);
            assertEquals("<script>alert('XSS');</script>", result);
        }

        @Test
        @DisplayName("Nên trả về chuỗi rỗng khi decode HTML entities null")
        void shouldReturnEmptyStringWhenDecodingNullHtmlEntities() {
            // Thực thi
            String result = EncodingHelper.htmlDecode(null);
            
            // Xác minh kết quả
            assertEquals("", result);
        }

        @Test
        @DisplayName("Nên trả về chuỗi rỗng khi decode HTML entities trống")
        void shouldReturnEmptyStringWhenDecodingEmptyHtmlEntities() {
            // Thực thi
            String result = EncodingHelper.htmlDecode("");
            
            // Xác minh kết quả
            assertEquals("", result);
        }

        @Test
        @DisplayName("Nên encode và decode HTML entities phổ biến")
        void shouldEncodeAndDecodeCommonHtmlEntities() {
            // Chuẩn bị dữ liệu
            String text = "\"Hello\" & <World>";
            
            // Thực thi
            String encoded = EncodingHelper.htmlEncode(text);
            String decoded = EncodingHelper.htmlDecode(encoded);
            
            // Xác minh kết quả
            assertNotNull(encoded);
            assertNotNull(decoded);
            assertEquals("&quot;Hello&quot; &amp; &lt;World&gt;", encoded);
            assertEquals(text, decoded);
        }
    }

    @Nested
    @DisplayName("Kiểm thử Charset Utilities")
    class CharsetTests {

        @Test
        @DisplayName("Nên chuyển đổi charset từ UTF-8 sang ISO-8859-1")
        void shouldConvertCharsetFromUtf8ToIso88591() {
            // Chuẩn bị dữ liệu
            String text = "Hello World";
            Charset from = StandardCharsets.UTF_8;
            Charset to = StandardCharsets.ISO_8859_1;
            
            // Thực thi
            String result = EncodingHelper.convertCharset(text, from, to);
            
            // Xác minh kết quả
            assertNotNull(result);
            assertEquals(text, result); // Ký tự ASCII giống nhau trong cả hai charset
        }

        @Test
        @DisplayName("Nên trả về chuỗi rỗng khi chuyển đổi văn bản null")
        void shouldReturnEmptyStringWhenConvertingNullText() {
            // Thực thi
            String result = EncodingHelper.convertCharset(null, StandardCharsets.UTF_8, StandardCharsets.ISO_8859_1);
            
            // Xác minh kết quả
            assertEquals("", result);
        }

        @Test
        @DisplayName("Nên trả về chuỗi rỗng khi chuyển đổi văn bản trống")
        void shouldReturnEmptyStringWhenConvertingEmptyText() {
            // Thực thi
            String result = EncodingHelper.convertCharset("", StandardCharsets.UTF_8, StandardCharsets.ISO_8859_1);
            
            // Xác minh kết quả
            assertEquals("", result);
        }

        @Test
        @DisplayName("Nên chuyển đổi bytes thành string với charset được chỉ định")
        void shouldConvertBytesToStringWithSpecifiedCharset() {
            // Chuẩn bị dữ liệu
            byte[] data = "Hello World".getBytes(StandardCharsets.UTF_8);
            
            // Thực thi
            String result = EncodingHelper.bytesToString(data, StandardCharsets.UTF_8);
            
            // Xác minh kết quả
            assertNotNull(result);
            assertEquals("Hello World", result);
        }

        @Test
        @DisplayName("Nên trả về chuỗi rỗng khi chuyển đổi bytes null thành string")
        void shouldReturnEmptyStringWhenConvertingNullBytesToString() {
            // Thực thi
            String result = EncodingHelper.bytesToString(null, StandardCharsets.UTF_8);
            
            // Xác minh kết quả
            assertEquals("", result);
        }

        @Test
        @DisplayName("Nên trả về chuỗi rỗng khi chuyển đổi bytes trống thành string")
        void shouldReturnEmptyStringWhenConvertingEmptyBytesToString() {
            // Chuẩn bị dữ liệu
            byte[] data = new byte[0];
            
            // Thực thi
            String result = EncodingHelper.bytesToString(data, StandardCharsets.UTF_8);
            
            // Xác minh kết quả
            assertEquals("", result);
        }

        @Test
        @DisplayName("Nên chuyển đổi string thành bytes với charset được chỉ định")
        void shouldConvertStringToBytesWithSpecifiedCharset() {
            // Chuẩn bị dữ liệu
            String text = "Hello World";
            
            // Thực thi
            byte[] result = EncodingHelper.stringToBytes(text, StandardCharsets.UTF_8);
            
            // Xác minh kết quả
            assertNotNull(result);
            assertArrayEquals("Hello World".getBytes(StandardCharsets.UTF_8), result);
        }

        @Test
        @DisplayName("Nên trả về mảng byte rỗng khi chuyển đổi string null thành bytes")
        void shouldReturnEmptyByteArrayWhenConvertingNullStringToBytes() {
            // Thực thi
            byte[] result = EncodingHelper.stringToBytes(null, StandardCharsets.UTF_8);
            
            // Xác minh kết quả
            assertNotNull(result);
            assertEquals(0, result.length);
        }

        @Test
        @DisplayName("Nên trả về mảng byte rỗng khi chuyển đổi string trống thành bytes")
        void shouldReturnEmptyByteArrayWhenConvertingEmptyStringToBytes() {
            // Thực thi
            byte[] result = EncodingHelper.stringToBytes("", StandardCharsets.UTF_8);
            
            // Xác minh kết quả
            assertNotNull(result);
            assertEquals(0, result.length);
        }
    }

    @Nested
    @DisplayName("Kiểm thử ByteBuffer Base64")
    class ByteBufferBase64Tests {

        @Test
        @DisplayName("Nên encode ByteBuffer thành Base64")
        void shouldEncodeByteBufferToBase64() {
            // Chuẩn bị dữ liệu
            ByteBuffer buffer = ByteBuffer.wrap("Hello World".getBytes(StandardCharsets.UTF_8));
            
            // Thực thi
            ByteBuffer result = EncodingHelper.base64Encode(buffer);
            
            // Xác minh kết quả
            assertNotNull(result);
            String encoded = StandardCharsets.UTF_8.decode(result).toString();
            assertEquals("SGVsbG8gV29ybGQ=", encoded);
        }

        @Test
        @DisplayName("Nên trả về null khi encode ByteBuffer null")
        void shouldReturnNullWhenEncodingNullByteBuffer() {
            // Thực thi
            ByteBuffer result = EncodingHelper.base64Encode((ByteBuffer) null);
            
            // Xác minh kết quả
            assertNull(result);
        }

        @Test
        @DisplayName("Nên decode ByteBuffer Base64")
        void shouldDecodeBase64ByteBuffer() {
            // Chuẩn bị dữ liệu
            ByteBuffer buffer = ByteBuffer.wrap("SGVsbG8gV29ybGQ=".getBytes(StandardCharsets.UTF_8));
            
            // Thực thi
            ByteBuffer result = EncodingHelper.base64Decode(buffer);
            
            // Xác minh kết quả
            assertNotNull(result);
            String decoded = StandardCharsets.UTF_8.decode(result).toString();
            assertEquals("Hello World", decoded);
        }

        @Test
        @DisplayName("Nên trả về null khi decode ByteBuffer null")
        void shouldReturnNullWhenDecodingNullByteBuffer() {
            // Thực thi
            ByteBuffer result = EncodingHelper.base64Decode((ByteBuffer) null);
            
            // Xác minh kết quả
            assertNull(result);
        }

        @Test
        @DisplayName("Nên ném exception khi decode ByteBuffer Base64 không hợp lệ")
        void shouldThrowExceptionWhenDecodingInvalidBase64ByteBuffer() {
            // Chuẩn bị dữ liệu
            ByteBuffer buffer = ByteBuffer.wrap("Invalid Base64!@#".getBytes(StandardCharsets.UTF_8));
            
            // Thực thi & Xác minh kết quả
            assertThrows(IllegalArgumentException.class, () -> 
                EncodingHelper.base64Decode(buffer));
        }

        @Test
        @DisplayName("Nên xử lý ByteBuffer rỗng trong Base64 encoding")
        void shouldHandleEmptyByteBufferInBase64Encoding() {
            // Chuẩn bị dữ liệu
            ByteBuffer buffer = ByteBuffer.allocate(0);
            
            // Thực thi
            ByteBuffer result = EncodingHelper.base64Encode(buffer);
            
            // Xác minh kết quả
            assertNotNull(result);
            assertEquals(0, result.remaining());
        }

        @Test
        @DisplayName("Nên xử lý encoding và decoding hai chiều với ByteBuffer")
        void shouldHandleRoundTripEncodingAndDecodingWithByteBuffer() {
            // Chuẩn bị dữ liệu
            String originalText = "Test ByteBuffer encoding/decoding";
            ByteBuffer originalBuffer = ByteBuffer.wrap(originalText.getBytes(StandardCharsets.UTF_8));
            
            // Thực thi
            ByteBuffer encoded = EncodingHelper.base64Encode(originalBuffer);
            ByteBuffer decoded = EncodingHelper.base64Decode(encoded);
            
            // Xác minh kết quả
            assertNotNull(encoded);
            assertNotNull(decoded);
            String decodedText = StandardCharsets.UTF_8.decode(decoded).toString();
            assertEquals(originalText, decodedText);
        }
    }

    @Nested
    @DisplayName("Kiểm thử Edge Cases và Integration")
    class EdgeCasesAndIntegrationTests {

        @Test
        @DisplayName("Nên xử lý ký tự Unicode trong tất cả phương thức encoding")
        void shouldHandleUnicodeCharactersInAllEncodingMethods() {
            // Chuẩn bị dữ liệu
            String unicodeText = "🌟 Tiếng Việt 中文 العربية";
            
            // Thực thi & Xác minh kết quả - Base64
            String base64Encoded = EncodingHelper.base64Encode(unicodeText);
            String base64Decoded = EncodingHelper.base64DecodeToString(base64Encoded);
            assertEquals(unicodeText, base64Decoded);
            
            // Thực thi & Xác minh kết quả - Hex
            String hexEncoded = EncodingHelper.hexEncode(unicodeText);
            String hexDecoded = EncodingHelper.hexDecodeToString(hexEncoded);
            assertEquals(unicodeText, hexDecoded);
            
            // Thực thi & Xác minh kết quả - URL
            String urlEncoded = EncodingHelper.urlEncode(unicodeText);
            String urlDecoded = EncodingHelper.urlDecode(urlEncoded);
            assertEquals(unicodeText, urlDecoded);
        }

        @Test
        @DisplayName("Nên xử lý tập dữ liệu lớn một cách hiệu quả")
        void shouldHandleLargeDataSetsEfficiently() {
            // Chuẩn bị dữ liệu
            String largeText = "A".repeat(10000);
            
            // Thực thi & Xác minh kết quả - Base64
            String base64Encoded = EncodingHelper.base64Encode(largeText);
            String base64Decoded = EncodingHelper.base64DecodeToString(base64Encoded);
            assertEquals(largeText, base64Decoded);
            
            // Thực thi & Xác minh kết quả - Hex
            String hexEncoded = EncodingHelper.hexEncode(largeText);
            String hexDecoded = EncodingHelper.hexDecodeToString(hexEncoded);
            assertEquals(largeText, hexDecoded);
        }

        @Test
        @DisplayName("Nên xử lý ký tự đặc biệt trong HTML encoding")
        void shouldHandleSpecialCharactersInHtmlEncoding() {
            // Chuẩn bị dữ liệu
            String specialChars = "< > & \" ' \n \t \r";
            
            // Thực thi
            String encoded = EncodingHelper.htmlEncode(specialChars);
            String decoded = EncodingHelper.htmlDecode(encoded);
            
            // Xác minh kết quả
            assertNotNull(encoded);
            assertNotNull(decoded);
            assertEquals(specialChars, decoded);
            assertTrue(encoded.contains("&lt;"));
            assertTrue(encoded.contains("&gt;"));
            assertTrue(encoded.contains("&amp;"));
            assertTrue(encoded.contains("&quot;"));
        }

        @Test
        @DisplayName("Nên duy trì tính nhất quán qua các chuyển đổi charset khác nhau")
        void shouldMaintainConsistencyAcrossDifferentCharsetConversions() {
            // Chuẩn bị dữ liệu
            String text = "Hello World 123";
            
            // Thực thi
            String utf8ToIso = EncodingHelper.convertCharset(text, StandardCharsets.UTF_8, StandardCharsets.ISO_8859_1);
            String isoToUtf8 = EncodingHelper.convertCharset(utf8ToIso, StandardCharsets.ISO_8859_1, StandardCharsets.UTF_8);
            
            // Xác minh kết quả
            assertEquals(text, utf8ToIso);
            assertEquals(text, isoToUtf8);
        }
    }
}
