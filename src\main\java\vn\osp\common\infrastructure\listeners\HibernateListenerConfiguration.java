package vn.osp.common.infrastructure.listeners;

import jakarta.annotation.PostConstruct;
import jakarta.persistence.EntityManagerFactory;
import lombok.RequiredArgsConstructor;
import org.hibernate.event.service.spi.EventListenerRegistry;
import org.hibernate.event.spi.EventType;
import org.hibernate.internal.SessionFactoryImpl;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Lazy;

/**
 * Configuration class để đăng ký các Hibernate Event Listeners.
 */
@Configuration
@RequiredArgsConstructor
public class HibernateListenerConfiguration {

    private final AuditingEventListener auditingEventListener;
    private final SoftDeleteEventListener softDeleteEventListener;
    private final MultiTenantEventListener multiTenantEventListener;

    @Lazy
    private final EntityManagerFactory entityManagerFactory;

    @PostConstruct
    public void registerListeners() {
        SessionFactoryImpl sessionFactory = entityManagerFactory.unwrap(SessionFactoryImpl.class);
        EventListenerRegistry registry = sessionFactory.getServiceRegistry().getService(EventListenerRegistry.class);

        // Đăng ký Auditing Event Listeners
        registry.getEventListenerGroup(EventType.PRE_INSERT).appendListener(auditingEventListener);
        registry.getEventListenerGroup(EventType.PRE_UPDATE).appendListener(auditingEventListener);

        // Đăng ký Soft Delete Event Listener
        registry.getEventListenerGroup(EventType.PRE_DELETE).appendListener(softDeleteEventListener);

        // Đăng ký Multi-Tenant Event Listeners
        registry.getEventListenerGroup(EventType.PRE_INSERT).appendListener(multiTenantEventListener);
        registry.getEventListenerGroup(EventType.PRE_UPDATE).appendListener(multiTenantEventListener);
        registry.getEventListenerGroup(EventType.PRE_DELETE).appendListener(multiTenantEventListener);
    }
}
