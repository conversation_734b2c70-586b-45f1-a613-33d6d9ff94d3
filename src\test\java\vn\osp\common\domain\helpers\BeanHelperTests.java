package vn.osp.common.domain.helpers;

import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.*;

@DisplayName("BeanHelper Tests")
class BeanHelperTests {

    // Test classes để sử dụng trong các test case
    static class SourceObject {
        private String name;
        private Integer age;
        private String email;
        private Boolean active;

        public SourceObject() {}

        public SourceObject(String name, Integer age, String email, Boolean active) {
            this.name = name;
            this.age = age;
            this.email = email;
            this.active = active;
        }

        // Getters và setters
        public String getName() { return name; }
        public void setName(String name) { this.name = name; }
        
        public Integer getAge() { return age; }
        public void setAge(Integer age) { this.age = age; }
        
        public String getEmail() { return email; }
        public void setEmail(String email) { this.email = email; }
        
        public Boolean getActive() { return active; }
        public void setActive(Boolean active) { this.active = active; }
    }

    static class TargetObject {
        private String name;
        private Integer age;
        private String email;
        private Boolean active;

        public TargetObject() {}

        public TargetObject(String name, Integer age, String email, Boolean active) {
            this.name = name;
            this.age = age;
            this.email = email;
            this.active = active;
        }

        // Getters và setters
        public String getName() { return name; }
        public void setName(String name) { this.name = name; }
        
        public Integer getAge() { return age; }
        public void setAge(Integer age) { this.age = age; }
        
        public String getEmail() { return email; }
        public void setEmail(String email) { this.email = email; }
        
        public Boolean getActive() { return active; }
        public void setActive(Boolean active) { this.active = active; }
    }

    static class DifferentObject {
        private String description;
        private Long id;

        public DifferentObject() {}

        public DifferentObject(String description, Long id) {
            this.description = description;
            this.id = id;
        }

        public String getDescription() { return description; }
        public void setDescription(String description) { this.description = description; }
        
        public Long getId() { return id; }
        public void setId(Long id) { this.id = id; }
    }

    @Nested
    @DisplayName("Copy Method Tests")
    class CopyMethodTests {

        @Test
        @DisplayName("Nên copy tất cả properties từ source sang target object thành công")
        void shouldCopyAllPropertiesSuccessfully() {
            // Arrange
            SourceObject source = new SourceObject("John Doe", 30, "<EMAIL>", true);
            TargetObject target = new TargetObject();

            // Act
            BeanHelper.copy(source, target);

            // Assert
            assertEquals(source.getName(), target.getName());
            assertEquals(source.getAge(), target.getAge());
            assertEquals(source.getEmail(), target.getEmail());
            assertEquals(source.getActive(), target.getActive());
        }

        @Test
        @DisplayName("Nên copy cả các properties có giá trị null")
        void shouldCopyNullProperties() {
            // Arrange
            SourceObject source = new SourceObject(null, null, "<EMAIL>", null);
            TargetObject target = new TargetObject("Original Name", 25, "<EMAIL>", false);

            // Act
            BeanHelper.copy(source, target);

            // Assert
            assertNull(target.getName()); // null values được copy
            assertNull(target.getAge()); // null values được copy
            assertEquals("<EMAIL>", target.getEmail());
            assertNull(target.getActive()); // null values được copy
        }

        @Test
        @DisplayName("Nên ghi đè giá trị hiện tại của target object")
        void shouldOverrideExistingTargetValues() {
            // Arrange
            SourceObject source = new SourceObject("New Name", 35, "<EMAIL>", false);
            TargetObject target = new TargetObject("Old Name", 20, "<EMAIL>", true);

            // Act
            BeanHelper.copy(source, target);

            // Assert
            assertEquals("New Name", target.getName());
            assertEquals(35, target.getAge());
            assertEquals("<EMAIL>", target.getEmail());
            assertEquals(false, target.getActive());
        }

        @Test
        @DisplayName("Nên xử lý trường hợp source object rỗng")
        void shouldHandleEmptySourceObject() {
            // Arrange
            SourceObject source = new SourceObject();
            TargetObject target = new TargetObject("Original", 25, "<EMAIL>", true);

            // Act
            BeanHelper.copy(source, target);

            // Assert
            // Tất cả giá trị của target sẽ được ghi đè bởi null từ source
            assertNull(target.getName());
            assertNull(target.getAge());
            assertNull(target.getEmail());
            assertNull(target.getActive());
        }

        @Test
        @DisplayName("Nên xử lý trường hợp target object rỗng")
        void shouldHandleEmptyTargetObject() {
            // Arrange
            SourceObject source = new SourceObject("Test Name", 40, "<EMAIL>", true);
            TargetObject target = new TargetObject();

            // Act
            BeanHelper.copy(source, target);

            // Assert
            assertEquals("Test Name", target.getName());
            assertEquals(40, target.getAge());
            assertEquals("<EMAIL>", target.getEmail());
            assertEquals(true, target.getActive());
        }

        @Test
        @DisplayName("Nên chỉ copy các properties có tên giống nhau")
        void shouldOnlyCopyMatchingPropertyNames() {
            // Arrange
            SourceObject source = new SourceObject("Test", 25, "<EMAIL>", true);
            DifferentObject target = new DifferentObject();

            // Act
            BeanHelper.copy(source, target);

            // Assert
            // Không có property nào trùng tên nên target object không thay đổi
            assertNull(target.getDescription());
            assertNull(target.getId());
        }
    }

    @Nested
    @DisplayName("CopyNonNull Method Tests")
    class CopyNonNullMethodTests {

        @Test
        @DisplayName("Nên copy chỉ các properties không null từ source sang target")
        void shouldCopyOnlyNonNullProperties() {
            // Arrange
            SourceObject source = new SourceObject("John Doe", null, "<EMAIL>", null);
            TargetObject target = new TargetObject("Original Name", 25, "<EMAIL>", false);

            // Act
            BeanHelper.copyNonNull(source, target);

            // Assert
            assertEquals("John Doe", target.getName()); // non-null được copy
            assertEquals(25, target.getAge()); // null không được copy, giữ nguyên giá trị cũ
            assertEquals("<EMAIL>", target.getEmail()); // non-null được copy
            assertEquals(false, target.getActive()); // null không được copy, giữ nguyên giá trị cũ
        }

        @Test
        @DisplayName("Nên giữ nguyên tất cả giá trị của target khi source chỉ có null")
        void shouldKeepAllTargetValuesWhenSourceHasOnlyNulls() {
            // Arrange
            SourceObject source = new SourceObject(null, null, null, null);
            TargetObject target = new TargetObject("Original", 30, "<EMAIL>", true);

            // Act
            BeanHelper.copyNonNull(source, target);

            // Assert
            assertEquals("Original", target.getName());
            assertEquals(30, target.getAge());
            assertEquals("<EMAIL>", target.getEmail());
            assertEquals(true, target.getActive());
        }

        @Test
        @DisplayName("Nên copy tất cả properties khi source không có null values")
        void shouldCopyAllPropertiesWhenSourceHasNoNulls() {
            // Arrange
            SourceObject source = new SourceObject("New Name", 40, "<EMAIL>", false);
            TargetObject target = new TargetObject("Old Name", 20, "<EMAIL>", true);

            // Act
            BeanHelper.copyNonNull(source, target);

            // Assert
            assertEquals("New Name", target.getName());
            assertEquals(40, target.getAge());
            assertEquals("<EMAIL>", target.getEmail());
            assertEquals(false, target.getActive());
        }

        @Test
        @DisplayName("Nên copy một phần properties khi source có mix null và non-null")
        void shouldCopyPartialPropertiesWithMixedNullAndNonNull() {
            // Arrange
            SourceObject source = new SourceObject("Updated Name", 35, null, true);
            TargetObject target = new TargetObject("Old Name", 25, "<EMAIL>", false);

            // Act
            BeanHelper.copyNonNull(source, target);

            // Assert
            assertEquals("Updated Name", target.getName()); // non-null được copy
            assertEquals(35, target.getAge()); // non-null được copy
            assertEquals("<EMAIL>", target.getEmail()); // null không được copy, giữ nguyên
            assertEquals(true, target.getActive()); // non-null được copy
        }

        @Test
        @DisplayName("Nên xử lý trường hợp target object rỗng với copyNonNull")
        void shouldHandleEmptyTargetObjectWithCopyNonNull() {
            // Arrange
            SourceObject source = new SourceObject("Test", null, "<EMAIL>", null);
            TargetObject target = new TargetObject();

            // Act
            BeanHelper.copyNonNull(source, target);

            // Assert
            assertEquals("Test", target.getName()); // non-null được copy
            assertNull(target.getAge()); // null không được copy, target vẫn null
            assertEquals("<EMAIL>", target.getEmail()); // non-null được copy
            assertNull(target.getActive()); // null không được copy, target vẫn null
        }

        @Test
        @DisplayName("Nên xử lý trường hợp source object rỗng với copyNonNull")
        void shouldHandleEmptySourceObjectWithCopyNonNull() {
            // Arrange
            SourceObject source = new SourceObject();
            TargetObject target = new TargetObject("Keep", 50, "<EMAIL>", true);

            // Act
            BeanHelper.copyNonNull(source, target);

            // Assert
            // Tất cả giá trị của target được giữ nguyên vì source chỉ có null
            assertEquals("Keep", target.getName());
            assertEquals(50, target.getAge());
            assertEquals("<EMAIL>", target.getEmail());
            assertEquals(true, target.getActive());
        }
    }

    @Nested
    @DisplayName("Edge Cases Tests")
    class EdgeCasesTests {

        @Test
        @DisplayName("Nên ném Exception khi source object là null")
        void shouldThrowExceptionWhenSourceIsNull() {
            // Arrange
            TargetObject target = new TargetObject();

            // Act & Assert
            assertThrows(Exception.class, () -> {
                BeanHelper.copy(null, target);
            });
        }

        @Test
        @DisplayName("Nên ném Exception khi target object là null")
        void shouldThrowExceptionWhenTargetIsNull() {
            // Arrange
            SourceObject source = new SourceObject("Test", 25, "<EMAIL>", true);

            // Act & Assert
            assertThrows(Exception.class, () -> {
                BeanHelper.copy(source, null);
            });
        }

        @Test
        @DisplayName("Nên ném Exception khi source object là null với copyNonNull")
        void shouldThrowExceptionWhenSourceIsNullWithCopyNonNull() {
            // Arrange
            TargetObject target = new TargetObject();

            // Act & Assert
            assertThrows(Exception.class, () -> {
                BeanHelper.copyNonNull(null, target);
            });
        }

        @Test
        @DisplayName("Nên ném Exception khi target object là null với copyNonNull")
        void shouldThrowExceptionWhenTargetIsNullWithCopyNonNull() {
            // Arrange
            SourceObject source = new SourceObject("Test", 25, "<EMAIL>", true);

            // Act & Assert
            assertThrows(Exception.class, () -> {
                BeanHelper.copyNonNull(source, null);
            });
        }

        @Test
        @DisplayName("Nên xử lý trường hợp cả source và target đều rỗng")
        void shouldHandleBothSourceAndTargetEmpty() {
            // Arrange
            SourceObject source = new SourceObject();
            TargetObject target = new TargetObject();

            // Act & Assert - không nên ném exception
            assertDoesNotThrow(() -> {
                BeanHelper.copy(source, target);
            });

            assertDoesNotThrow(() -> {
                BeanHelper.copyNonNull(source, target);
            });
        }
    }
}
