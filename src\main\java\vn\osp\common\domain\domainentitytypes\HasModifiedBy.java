package vn.osp.common.domain.domainentitytypes;

import jakarta.annotation.Nullable;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * Model có modified by (người sửa) và modified (thời gian sửa).
 *
 * @param <TKey> Ki<PERSON>u dữ liệu của Id (UUID, Long, Integer, ...)
 */
public interface HasModifiedBy<TKey extends Serializable> {

    /**
     * Thời gian sửa.
     */
    @Nullable
    LocalDateTime getModifiedAt();

    void setModifiedAt(@Nullable LocalDateTime modified);

    /**
     * Id người sửa.
     */
    @Nullable
    TKey getModifiedBy();
    void setModifiedBy(@Nullable TKey modifiedBy);
}
