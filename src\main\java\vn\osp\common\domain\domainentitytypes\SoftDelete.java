package vn.osp.common.domain.domainentitytypes;

import jakarta.annotation.Nullable;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * Model có thời gian xóa và trạng thái đã xóa.
 *
 * @param <TKey> Ki<PERSON>u dữ liệu của Id (UUID, Long, Integer, ...)
 */
public interface SoftDelete<TKey extends Serializable> {

    /**
     * Thời gian xóa.
     */
    @Nullable
    LocalDateTime getDeletedAt();

    void setDeletedAt(@Nullable LocalDateTime deleted);

    /**
     * Trạng thái đã xóa hay chưa.
     */
    boolean isDeleted();

    void setDeleted(boolean isDeleted);

    /**
     * Id người xóa.
     */
    @Nullable
    TKey getDeletedBy();
    void setDeletedBy(@Nullable TKey deletedBy);
}