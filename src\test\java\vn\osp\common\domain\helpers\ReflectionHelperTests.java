package vn.osp.common.domain.helpers;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.Nested;
import vn.osp.common.domain.helpers.ReflectionHelper.ReflectionException;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;
import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.util.List;
import java.util.Map;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.*;

@DisplayName("ReflectionHelper Tests")
class ReflectionHelperTests {

    @BeforeEach
    void setUp() {
        // Setup test data if needed
    }

    @Nested
    @DisplayName("Class & Type Tests")
    class ClassAndTypeTests {

        @Test
        @DisplayName("Nên lấy được Class object từ tên class hợp lệ")
        void shouldGetClassFromValidClassName() {
            // When
            Class<?> clazz = ReflectionHelper.getClass("java.lang.String");

            // Then
            assertEquals(String.class, clazz);
        }

        @Test
        @DisplayName("Nên throw ReflectionException khi không tìm thấy class")
        void shouldThrowExceptionWhenClassNotFound() {
            // When & Then
            assertThrows(ReflectionException.class, () -> 
                ReflectionHelper.getClass("non.existent.Class"));
        }

        @Test
        @DisplayName("Nên lấy được tất cả fields bao gồm cả field kế thừa")
        void shouldGetAllFieldsIncludingInherited() {
            // When
            List<Field> fields = ReflectionHelper.getAllFields(TestChild.class);

            // Then
            assertFalse(fields.isEmpty());
            assertTrue(fields.stream().anyMatch(f -> f.getName().equals("childField")));
            assertTrue(fields.stream().anyMatch(f -> f.getName().equals("parentField")));
        }

        @Test
        @DisplayName("Nên lấy được tất cả methods bao gồm cả method kế thừa")
        void shouldGetAllMethodsIncludingInherited() {
            // When
            List<Method> methods = ReflectionHelper.getAllMethods(TestChild.class);

            // Then
            assertFalse(methods.isEmpty());
            assertTrue(methods.stream().anyMatch(m -> m.getName().equals("childMethod")));
            assertTrue(methods.stream().anyMatch(m -> m.getName().equals("parentMethod")));
        }

        @Test
        @DisplayName("Nên kiểm tra được quan hệ kế thừa subclass")
        void shouldCheckSubclassRelationship() {
            // When & Then
            assertTrue(ReflectionHelper.isSubclassOf(TestChild.class, TestParent.class));
            assertFalse(ReflectionHelper.isSubclassOf(TestParent.class, TestChild.class));
            assertFalse(ReflectionHelper.isSubclassOf(TestParent.class, TestParent.class));
        }

        @Test
        @DisplayName("Nên kiểm tra được implementation của interface")
        void shouldCheckInterfaceImplementation() {
            // When & Then
            assertTrue(ReflectionHelper.implementsInterface(TestChild.class, TestInterface.class));
            assertFalse(ReflectionHelper.implementsInterface(TestParent.class, TestInterface.class));
        }

        @Test
        @DisplayName("Nên kiểm tra được quan hệ assignable")
        void shouldCheckAssignableRelationship() {
            // When & Then
            assertTrue(ReflectionHelper.isAssignableFrom(TestChild.class, TestParent.class));
            assertTrue(ReflectionHelper.isAssignableFrom(TestChild.class, TestInterface.class));
            assertFalse(ReflectionHelper.isAssignableFrom(TestParent.class, TestChild.class));
        }
    }

    @Nested
    @DisplayName("Field Tests")
    class FieldTests {

        private TestChild testObject;

        @BeforeEach
        void setUp() {
            testObject = new TestChild("child", "parent");
        }

        @Test
        @DisplayName("Nên lấy được Field object từ tên field hợp lệ")
        void shouldGetFieldFromValidName() {
            // When
            Field field = ReflectionHelper.getField(TestChild.class, "childField");

            // Then
            assertNotNull(field);
            assertEquals("childField", field.getName());
        }

        @Test
        @DisplayName("Nên throw ReflectionException khi không tìm thấy field")
        void shouldThrowExceptionWhenFieldNotFound() {
            // When & Then
            assertThrows(ReflectionException.class, () -> 
                ReflectionHelper.getField(TestChild.class, "nonExistentField"));
        }

        @Test
        @DisplayName("Nên kiểm tra được sự tồn tại của field")
        void shouldCheckFieldExistence() {
            // When & Then
            assertTrue(ReflectionHelper.hasField(TestChild.class, "childField"));
            assertTrue(ReflectionHelper.hasField(TestChild.class, "parentField"));
            assertFalse(ReflectionHelper.hasField(TestChild.class, "nonExistentField"));
        }

        @Test
        @DisplayName("Nên lấy được giá trị field")
        void shouldGetFieldValue() {
            // When
            Object childValue = ReflectionHelper.getFieldValue(testObject, "childField");
            Object parentValue = ReflectionHelper.getFieldValue(testObject, "parentField");

            // Then
            assertEquals("child", childValue);
            assertEquals("parent", parentValue);
        }

        @Test
        @DisplayName("Nên set được giá trị field")
        void shouldSetFieldValue() {
            // Given
            String newValue = "newChild";

            // When
            ReflectionHelper.setFieldValue(testObject, "childField", newValue);

            // Then
            assertEquals(newValue, ReflectionHelper.getFieldValue(testObject, "childField"));
        }

        @Test
        @DisplayName("Nên lấy được tất cả field values dưới dạng Map")
        void shouldGetAllFieldValuesAsMap() {
            // When
            Map<String, Object> fieldValues = ReflectionHelper.getAllFieldValues(testObject);

            // Then
            assertNotNull(fieldValues);
            assertEquals("child", fieldValues.get("childField"));
            assertEquals("parent", fieldValues.get("parentField"));
        }
    }

    @Nested
    @DisplayName("Method Tests")
    class MethodTests {

        private TestChild testObject;

        @BeforeEach
        void setUp() {
            testObject = new TestChild("child", "parent");
        }

        @Test
        @DisplayName("Nên lấy được Method object từ tên method hợp lệ")
        void shouldGetMethodFromValidName() {
            // When
            Method method = ReflectionHelper.getMethod(TestChild.class, "childMethod");

            // Then
            assertNotNull(method);
            assertEquals("childMethod", method.getName());
        }

        @Test
        @DisplayName("Nên throw ReflectionException khi không tìm thấy method")
        void shouldThrowExceptionWhenMethodNotFound() {
            // When & Then
            assertThrows(ReflectionException.class, () -> 
                ReflectionHelper.getMethod(TestChild.class, "nonExistentMethod"));
        }

        @Test
        @DisplayName("Nên kiểm tra được sự tồn tại của method")
        void shouldCheckMethodExistence() {
            // When & Then
            assertTrue(ReflectionHelper.hasMethod(TestChild.class, "childMethod"));
            assertTrue(ReflectionHelper.hasMethod(TestChild.class, "parentMethod"));
            assertFalse(ReflectionHelper.hasMethod(TestChild.class, "nonExistentMethod"));
        }

        @Test
        @DisplayName("Nên invoke được method với tham số")
        void shouldInvokeMethodWithParameters() {
            // When
            Object result = ReflectionHelper.invokeMethod(testObject, "setChildField", "newValue");

            // Then
            assertNull(result); // void method
            assertEquals("newValue", ReflectionHelper.getFieldValue(testObject, "childField"));
        }

        @Test
        @DisplayName("Nên invoke được method không có tham số")
        void shouldInvokeMethodWithoutParameters() {
            // When
            Object result = ReflectionHelper.invokeMethod(testObject, "getChildField");

            // Then
            assertEquals("child", result);
        }
    }

    @Nested
    @DisplayName("Constructor Tests")
    class ConstructorTests {

        @Test
        @DisplayName("Nên tạo được instance mới với constructor không tham số")
        void shouldCreateNewInstanceWithNoArgsConstructor() {
            // When
            Object instance = ReflectionHelper.newInstance(TestParent.class);

            // Then
            assertNotNull(instance);
            assertInstanceOf(TestParent.class, instance);
        }

        @Test
        @DisplayName("Nên tạo được instance mới với constructor có tham số")
        void shouldCreateNewInstanceWithParameterizedConstructor() {
            // When
            Object instance = ReflectionHelper.newInstance(TestChild.class, "child", "parent");

            // Then
            assertNotNull(instance);
            assertInstanceOf(TestChild.class, instance);
            assertEquals("child", ReflectionHelper.getFieldValue(instance, "childField"));
        }

        @Test
        @DisplayName("Nên throw ReflectionException khi không tìm thấy constructor phù hợp")
        void shouldThrowExceptionWhenConstructorNotFound() {
            // When & Then
            assertThrows(ReflectionException.class, () -> 
                ReflectionHelper.newInstance(TestChild.class, 123, 456, 789));
        }
    }

    @Nested
    @DisplayName("Annotation Tests")
    class AnnotationTests {

        @Test
        @DisplayName("Nên kiểm tra được sự tồn tại của annotation")
        void shouldCheckAnnotationPresence() {
            // When & Then
            assertTrue(ReflectionHelper.hasAnnotation(TestChild.class, TestAnnotation.class));
            assertFalse(ReflectionHelper.hasAnnotation(TestParent.class, TestAnnotation.class));
        }

        @Test
        @DisplayName("Nên lấy được annotation từ element")
        void shouldGetAnnotationFromElement() {
            // When
            Optional<TestAnnotation> annotation = ReflectionHelper.getAnnotation(TestChild.class, TestAnnotation.class);

            // Then
            assertTrue(annotation.isPresent());
            assertEquals("test", annotation.get().value());
        }

        @Test
        @DisplayName("Nên trả về Optional.empty() khi annotation không tồn tại")
        void shouldReturnEmptyOptionalWhenAnnotationNotPresent() {
            // When
            Optional<TestAnnotation> annotation = ReflectionHelper.getAnnotation(TestParent.class, TestAnnotation.class);

            // Then
            assertTrue(annotation.isEmpty());
        }
    }

    @Nested
    @DisplayName("Misc Tests")
    class MiscTests {

        @Test
        @DisplayName("Nên lấy được generic type của field")
        void shouldGetGenericTypeOfField() throws NoSuchFieldException {
            // Given
            Field listField = TestGeneric.class.getDeclaredField("stringList");
            Field nonGenericField = TestGeneric.class.getDeclaredField("objectField");

            // When
            Class<?> genericType = ReflectionHelper.getGenericType(listField);
            Class<?> nonGenericType = ReflectionHelper.getGenericType(nonGenericField);

            // Then
            assertEquals(String.class, genericType);
            assertEquals(Object.class, nonGenericType);
        }

        @Test
        @DisplayName("Nên copy được properties từ source sang target")
        void shouldCopyPropertiesFromSourceToTarget() {
            // Given
            TestChild source = new TestChild("sourceChild", "sourceParent");
            TestChild target = new TestChild("targetChild", "targetParent");

            // When
            ReflectionHelper.copyProperties(source, target);

            // Then
            assertEquals("sourceChild", ReflectionHelper.getFieldValue(target, "childField"));
            assertEquals("sourceParent", ReflectionHelper.getFieldValue(target, "parentField"));
        }
    }

    @Nested
    @DisplayName("ReflectionException Tests")
    class ReflectionExceptionTests {

        @Test
        @DisplayName("Nên tạo được ReflectionException với message")
        void shouldCreateReflectionExceptionWithMessage() {
            // Given
            String message = "Test exception message";

            // When
            ReflectionException exception = new ReflectionException(message);

            // Then
            assertEquals(message, exception.getMessage());
        }

        @Test
        @DisplayName("Nên tạo được ReflectionException với message và cause")
        void shouldCreateReflectionExceptionWithMessageAndCause() {
            // Given
            String message = "Test exception message";
            RuntimeException cause = new RuntimeException("Cause");

            // When
            ReflectionException exception = new ReflectionException(message, cause);

            // Then
            assertEquals(message, exception.getMessage());
            assertEquals(cause, exception.getCause());
        }
    }

    // ===== Test Classes =====

    @Retention(RetentionPolicy.RUNTIME)
    @Target(ElementType.TYPE)
    @interface TestAnnotation {
        String value() default "";
    }

    interface TestInterface {
        void interfaceMethod();
    }

    static class TestParent {
        protected String parentField;

        public TestParent() {
            this.parentField = "default";
        }

        public TestParent(String parentField) {
            this.parentField = parentField;
        }

        public String getParentField() {
            return parentField;
        }

        public void setParentField(String parentField) {
            this.parentField = parentField;
        }

        public void parentMethod() {
            // Test method
        }
    }

    @TestAnnotation("test")
    static class TestChild extends TestParent implements TestInterface {
        private String childField;

        public TestChild() {
            super();
            this.childField = "default";
        }

        public TestChild(String childField, String parentField) {
            super(parentField);
            this.childField = childField;
        }

        public String getChildField() {
            return childField;
        }

        public void setChildField(String childField) {
            this.childField = childField;
        }

        public void childMethod() {
            // Test method
        }

        @Override
        public void interfaceMethod() {
            // Implementation
        }
    }

    static class TestGeneric {
        private List<String> stringList;
        private Object objectField;
    }
}