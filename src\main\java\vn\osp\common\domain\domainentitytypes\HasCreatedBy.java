package vn.osp.common.domain.domainentitytypes;

import jakarta.annotation.Nullable;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * Model có created by (người tạo) và created (thời gian tạo)
 *
 * @param <TKey> kiểu dữ liệu k<PERSON> (thường là UUID, Long, Integer,...)
 */
public interface HasCreatedBy<TKey extends Serializable> {

    /**
     * Thời gian tạo
     */
    LocalDateTime getCreatedAt();

    void setCreatedAt(LocalDateTime created);

    /**
     * Id người tạo
     */
    @Nullable
    TKey getCreatedBy();
    void setCreatedBy(@Nullable TKey createdBy);
}
