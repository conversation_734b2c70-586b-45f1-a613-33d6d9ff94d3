package vn.osp.common.domain.helpers;

import org.apache.commons.text.StringEscapeUtils;

import java.net.URLDecoder;
import java.net.URLEncoder;
import java.nio.ByteBuffer;
import java.nio.charset.Charset;
import java.nio.charset.StandardCharsets;
import java.util.Base64;

public final class EncodingHelper {
    private EncodingHelper() {
    }

    /* ==========================
       BASE64 Utilities
    ========================== */

    /**
     * Encode mảng byte thành chuỗi Base64.
     * 
     * @param data mảng byte cần encode
     * @return chuỗi Base64 hoặc chuỗi rỗng nếu input null/empty
     */
    public static String base64Encode(byte[] data) {
        if (data == null || data.length == 0) {
            return StringHelper.EMPTY;
        }
        return Base64.getEncoder().encodeToString(data);
    }

    /**
     * Decode chuỗi Base64 thành mảng byte.
     * 
     * @param base64 chuỗi Base64 cần decode
     * @return mảng byte sau khi decode hoặc mảng rỗng nếu input null/empty
     * @throws IllegalArgumentException nếu chuỗi Base64 không hợp lệ
     */
    public static byte[] base64Decode(String base64) {
        if (StringHelper.isEmpty(base64)) {
            return new byte[0];
        }
        return Base64.getDecoder().decode(base64);
    }

    /**
     * Encode chuỗi văn bản thành Base64 sử dụng UTF-8 encoding.
     * 
     * @param text chuỗi văn bản cần encode
     * @return chuỗi Base64 hoặc chuỗi rỗng nếu input null/empty
     */
    public static String base64Encode(String text) {
        if (StringHelper.isEmpty(text)) {
            return StringHelper.EMPTY;
        }
        return base64Encode(text.getBytes(StandardCharsets.UTF_8));
    }

    /**
     * Decode chuỗi Base64 về dạng String sử dụng UTF-8 encoding.
     * 
     * @param base64 chuỗi Base64 cần decode
     * @return chuỗi văn bản sau khi decode hoặc chuỗi rỗng nếu input null/empty
     * @throws IllegalArgumentException nếu chuỗi Base64 không hợp lệ
     */
    public static String base64DecodeToString(String base64) {
        if (StringHelper.isEmpty(base64)) {
            return StringHelper.EMPTY;
        }
        return new String(base64Decode(base64), StandardCharsets.UTF_8);
    }

    /**
     * Encode mảng byte thành chuỗi Base64 URL-safe (không có padding).
     * 
     * @param data mảng byte cần encode
     * @return chuỗi Base64 URL-safe hoặc chuỗi rỗng nếu input null/empty
     */
    public static String base64UrlEncode(byte[] data) {
        if (data == null || data.length == 0) {
            return StringHelper.EMPTY;
        }
        return Base64.getUrlEncoder().withoutPadding().encodeToString(data);
    }

    /**
     * Decode chuỗi Base64 URL-safe thành mảng byte.
     * 
     * @param base64 chuỗi Base64 URL-safe cần decode
     * @return mảng byte sau khi decode hoặc mảng rỗng nếu input null/empty
     * @throws IllegalArgumentException nếu chuỗi Base64 không hợp lệ
     */
    public static byte[] base64UrlDecode(String base64) {
        if (StringHelper.isEmpty(base64)) {
            return new byte[0];
        }
        return Base64.getUrlDecoder().decode(base64);
    }

    /**
     * Encode mảng byte thành chuỗi Base64 MIME với line breaks.
     * 
     * @param data mảng byte cần encode
     * @return chuỗi Base64 MIME hoặc chuỗi rỗng nếu input null/empty
     */
    public static String base64MimeEncode(byte[] data) {
        if (data == null || data.length == 0) {
            return StringHelper.EMPTY;
        }
        return Base64.getMimeEncoder().encodeToString(data);
    }

    /**
     * Decode chuỗi Base64 MIME thành mảng byte.
     * 
     * @param base64 chuỗi Base64 MIME cần decode
     * @return mảng byte sau khi decode hoặc mảng rỗng nếu input null/empty
     * @throws IllegalArgumentException nếu chuỗi Base64 không hợp lệ
     */
    public static byte[] base64MimeDecode(String base64) {
        if (StringHelper.isEmpty(base64)) {
            return new byte[0];
        }
        return Base64.getMimeDecoder().decode(base64);
    }


    /* ==========================
       HEX Utilities
    ========================== */

    /**
     * Encode mảng byte thành chuỗi hexadecimal.
     * 
     * @param data mảng byte cần encode
     * @return chuỗi hex hoặc chuỗi rỗng nếu input null/empty
     */
    public static String hexEncode(byte[] data) {
        if (data == null || data.length == 0) return StringHelper.EMPTY;
        StringBuilder sb = new StringBuilder(data.length * 2);
        for (byte b : data) {
            sb.append(String.format("%02x", b));
        }
        return sb.toString();
    }

    /**
     * Decode chuỗi hexadecimal thành mảng byte.
     * 
     * @param hex chuỗi hex cần decode
     * @return mảng byte sau khi decode hoặc mảng rỗng nếu input null/empty
     * @throws IllegalArgumentException nếu chuỗi hex không hợp lệ hoặc có độ dài lẻ
     */
    public static byte[] hexDecode(String hex) {
        if (StringHelper.isEmpty(hex)) return new byte[0];
        if (hex.length() % 2 != 0) {
            throw new IllegalArgumentException("Invalid hex string length");
        }
        int len = hex.length();
        byte[] result = new byte[len / 2];
        for (int i = 0; i < len; i += 2) {
            int hi = Character.digit(hex.charAt(i), 16);
            int lo = Character.digit(hex.charAt(i + 1), 16);
            if (hi == -1 || lo == -1) {
                throw new IllegalArgumentException("Invalid hex character");
            }
            result[i / 2] = (byte) ((hi << 4) + lo);
        }
        return result;
    }

    /**
     * Encode chuỗi văn bản thành hexadecimal sử dụng UTF-8 encoding.
     * 
     * @param text chuỗi văn bản cần encode
     * @return chuỗi hex hoặc chuỗi rỗng nếu input null/empty
     */
    public static String hexEncode(String text) {
        if (StringHelper.isEmpty(text)) return StringHelper.EMPTY;
        return hexEncode(text.getBytes(StandardCharsets.UTF_8));
    }

    /**
     * Decode chuỗi hexadecimal về dạng String sử dụng UTF-8 encoding.
     * 
     * @param hex chuỗi hex cần decode
     * @return chuỗi văn bản sau khi decode hoặc chuỗi rỗng nếu input null/empty
     * @throws IllegalArgumentException nếu chuỗi hex không hợp lệ
     */
    public static String hexDecodeToString(String hex) {
        if (StringHelper.isEmpty(hex)) return StringHelper.EMPTY;
        return new String(hexDecode(hex), StandardCharsets.UTF_8);
    }


    /* ==========================
       URL Encoding / Decoding
    ========================== */

    /**
     * Encode chuỗi văn bản thành định dạng URL-encoded sử dụng UTF-8 charset.
     * 
     * @param text chuỗi văn bản cần encode
     * @return chuỗi URL-encoded hoặc chuỗi rỗng nếu input null/empty
     */
    public static String urlEncode(String text) {
        if (StringHelper.isEmpty(text)) return StringHelper.EMPTY;
        return URLEncoder.encode(text, StandardCharsets.UTF_8);
    }

    /**
     * Decode chuỗi URL-encoded về dạng văn bản gốc sử dụng UTF-8 charset.
     * 
     * @param encoded chuỗi URL-encoded cần decode
     * @return chuỗi văn bản sau khi decode hoặc chuỗi rỗng nếu input null/empty
     */
    public static String urlDecode(String encoded) {
        if (StringHelper.isEmpty(encoded)) return StringHelper.EMPTY;
        return URLDecoder.decode(encoded, StandardCharsets.UTF_8);
    }

    /* ==========================
       HTML Encoding / Decoding (Apache Commons Text)
    ========================== */

    /**
     * Encode chuỗi văn bản thành HTML entities để tránh XSS attacks.
     * Chuyển đổi các ký tự đặc biệt như &lt;, &gt;, &amp;, &quot; thành HTML entities.
     * 
     * @param text chuỗi văn bản cần encode
     * @return chuỗi HTML-encoded hoặc chuỗi rỗng nếu input null/empty
     */
    public static String htmlEncode(String text) {
        if (StringHelper.isEmpty(text)) return StringHelper.EMPTY;
        return StringEscapeUtils.escapeHtml4(text);
    }

    /**
     * Decode chuỗi HTML entities về dạng văn bản gốc.
     * Chuyển đổi các HTML entities như &lt;, &gt;, &amp;, &quot; về ký tự gốc.
     * 
     * @param text chuỗi HTML-encoded cần decode
     * @return chuỗi văn bản sau khi decode hoặc chuỗi rỗng nếu input null/empty
     */
    public static String htmlDecode(String text) {
        if (StringHelper.isEmpty(text)) return StringHelper.EMPTY;
        return StringEscapeUtils.unescapeHtml4(text);
    }


    /* ==========================
       Charset Utilities
    ========================== */

    /**
     * Chuyển đổi encoding của chuỗi văn bản từ charset này sang charset khác.
     * 
     * @param text chuỗi văn bản cần chuyển đổi
     * @param from charset nguồn
     * @param to charset đích
     * @return chuỗi văn bản với encoding mới hoặc chuỗi rỗng nếu input null/empty
     */
    public static String convertCharset(String text, Charset from, Charset to) {
        if (StringHelper.isEmpty(text)) return StringHelper.EMPTY;
        byte[] bytes = text.getBytes(from);
        return new String(bytes, to);
    }

    /**
     * Chuyển đổi mảng byte thành chuỗi văn bản sử dụng charset được chỉ định.
     * 
     * @param data mảng byte cần chuyển đổi
     * @param charset charset để decode
     * @return chuỗi văn bản sau khi decode hoặc chuỗi rỗng nếu input null/empty
     */
    public static String bytesToString(byte[] data, Charset charset) {
        if (data == null || data.length == 0) return StringHelper.EMPTY;
        return new String(data, charset);
    }

    /**
     * Chuyển đổi chuỗi văn bản thành mảng byte sử dụng charset được chỉ định.
     * 
     * @param text chuỗi văn bản cần chuyển đổi
     * @param charset charset để encode
     * @return mảng byte sau khi encode hoặc mảng rỗng nếu input null/empty
     */
    public static byte[] stringToBytes(String text, Charset charset) {
        if (StringHelper.isEmpty(text)) return new byte[0];
        return text.getBytes(charset);
    }

    /**
     * Encode ByteBuffer thành Base64 format.
     * 
     * @param buffer ByteBuffer cần encode
     * @return ByteBuffer chứa dữ liệu Base64 hoặc null nếu input null
     */
    public static ByteBuffer base64Encode(ByteBuffer buffer) {
        if (buffer == null) return null;
        return Base64.getEncoder().encode(buffer);
    }

    /**
     * Decode ByteBuffer từ Base64 format.
     * 
     * @param buffer ByteBuffer chứa dữ liệu Base64 cần decode
     * @return ByteBuffer chứa dữ liệu sau khi decode hoặc null nếu input null
     * @throws IllegalArgumentException nếu dữ liệu Base64 không hợp lệ
     */
    public static ByteBuffer base64Decode(ByteBuffer buffer) {
        if (buffer == null) return null;
        return Base64.getDecoder().decode(buffer);
    }
}
