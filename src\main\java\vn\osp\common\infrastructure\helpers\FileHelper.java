package vn.osp.common.infrastructure.helpers;

import java.io.IOException;
import java.nio.file.*;
import java.nio.file.attribute.BasicFileAttributes;

public final class FileHelper {
    private FileHelper() {
    }

    /**
     * Copy toàn bộ file và thư mục con từ sourceDir sang targetDir
     *
     * @param sourceDir Path thư mục nguồn
     * @param targetDir Path thư mục đích
     * @throws IOException nếu lỗi I/O xảy ra
     */
    public static void copyDirectory(Path sourceDir, Path targetDir) throws IOException {
        if (!Files.exists(sourceDir) || !Files.isDirectory(sourceDir)) {
            throw new IllegalArgumentException("Source directory does not exist or is not a directory");
        }

        // Tạo folder đích nếu chưa tồn tại
        if (!Files.exists(targetDir)) {
            Files.createDirectories(targetDir);
        }

        // Walk file tree
        Files.walkFileTree(sourceDir, new SimpleFileVisitor<>() {
            @Override
            public FileVisitResult preVisitDirectory(Path dir, BasicFileAttributes attrs) throws IOException {
                Path targetPath = targetDir.resolve(sourceDir.relativize(dir));
                if (!Files.exists(targetPath)) {
                    Files.createDirectory(targetPath);
                }
                return FileVisitResult.CONTINUE;
            }

            @Override
            public FileVisitResult visitFile(Path file, BasicFileAttributes attrs) throws IOException {
                Path targetPath = targetDir.resolve(sourceDir.relativize(file));
                Files.copy(file, targetPath, StandardCopyOption.REPLACE_EXISTING);
                return FileVisitResult.CONTINUE;
            }
        });
    }
}
