package vn.osp.common.domain.helpers;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.NullAndEmptySource;
import org.junit.jupiter.params.provider.CsvSource;

import static org.junit.jupiter.api.Assertions.*;

@DisplayName("Kiểm thử StringHelper")
class StringHelperTests {

    @Test
    @DisplayName("isEmpty - trả về true cho chuỗi null, rỗng và blank")
    void isEmpty_shouldReturnTrueForNullEmptyAndBlankStrings() {
        assertTrue(StringHelper.isEmpty(null));
        assertTrue(StringHelper.isEmpty(""));
        assertTrue(StringHelper.isEmpty("   "));
        assertTrue(StringHelper.isEmpty("\t\n\r"));
    }

    @Test
    @DisplayName("isEmpty - trả về false cho chuỗi không rỗng")
    void isEmpty_shouldReturnFalseForNonEmptyStrings() {
        assertFalse(StringHelper.isEmpty("a"));
        assertFalse(StringHelper.isEmpty("hello"));
        assertFalse(StringHelper.isEmpty(" hello "));
        assertFalse(StringHelper.isEmpty("0"));
    }

    @Test
    @DisplayName("defaultIfEmpty - trả về giá trị mặc định cho chuỗi rỗng")
    void defaultIfEmpty_shouldReturnDefaultValueForEmptyStrings() {
        assertEquals("default", StringHelper.defaultIfEmpty(null, "default"));
        assertEquals("default", StringHelper.defaultIfEmpty("", "default"));
        assertEquals("default", StringHelper.defaultIfEmpty("   ", "default"));
        assertEquals("default", StringHelper.defaultIfEmpty("\t", "default"));
    }

    @Test
    @DisplayName("defaultIfEmpty - trả về giá trị ban đầu cho chuỗi không rỗng")
    void defaultIfEmpty_shouldReturnOriginalValueForNonEmptyStrings() {
        assertEquals("hello", StringHelper.defaultIfEmpty("hello", "default"));
        assertEquals(" world ", StringHelper.defaultIfEmpty(" world ", "default"));
        assertEquals("0", StringHelper.defaultIfEmpty("0", "default"));
    }

    @Test
    @DisplayName("safeTrim - trim chuỗi một cách an toàn")
    void safeTrim_shouldTrimStringsSafely() {
        assertNull(StringHelper.safeTrim(null));
        assertEquals("", StringHelper.safeTrim(""));
        assertEquals("", StringHelper.safeTrim("   "));
        assertEquals("hello", StringHelper.safeTrim("  hello  "));
        assertEquals("hello world", StringHelper.safeTrim("\t hello world \n"));
    }

    @Test
    @DisplayName("randomNumeric - tạo chuỗi số với độ dài chính xác")
    void randomNumeric_shouldGenerateNumericStringWithCorrectLength() {
        String result5 = StringHelper.randomNumeric(5);
        assertEquals(5, result5.length());
        assertTrue(result5.matches("[0-9]+"));

        String result10 = StringHelper.randomNumeric(10);
        assertEquals(10, result10.length());
        assertTrue(result10.matches("[0-9]+"));
    }

    @Test
    @DisplayName("randomNumeric - ném exception cho độ dài không hợp lệ")
    void randomNumeric_shouldThrowExceptionForInvalidLength() {
        assertThrows(IllegalArgumentException.class, () -> StringHelper.randomNumeric(0));
        assertThrows(IllegalArgumentException.class, () -> StringHelper.randomNumeric(-1));
    }

    @Test
    @DisplayName("random - tạo chuỗi từ tập ký tự được chấp nhận")
    void random_shouldGenerateStringFromAcceptCharacters() {
        String result = StringHelper.random(5, "abc");
        assertEquals(5, result.length());
        assertTrue(result.matches("[abc]+"));

        String result2 = StringHelper.random(3, "XYZ123");
        assertEquals(3, result2.length());
        assertTrue(result2.matches("[XYZ123]+"));
    }

    @Test
    @DisplayName("random - xử lý các trường hợp biên")
    void random_shouldHandleEdgeCases() {
        assertEquals("", StringHelper.random(0, "abc"));
        
        String result = StringHelper.random(5, "a");
        assertEquals("aaaaa", result);
    }

    @Test
    @DisplayName("random - ném exception cho tham số không hợp lệ")
    void random_shouldThrowExceptionForInvalidParameters() {
        assertThrows(IllegalArgumentException.class, () -> StringHelper.random(-1, "abc"));
        assertThrows(IllegalArgumentException.class, () -> StringHelper.random(5, null));
        assertThrows(IllegalArgumentException.class, () -> StringHelper.random(5, ""));
    }

    @ParameterizedTest
    @CsvSource({
        "'', ''",
        "'Nguyen Van A', 'Nguyen Van A'",
        "'Nguyễn Văn A', 'Nguyen Van A'",
        "'Trần Thị Bình', 'Tran Thi Binh'",
        "'Đào Xuân Cường', 'Dao Xuan Cuong'",
        "'Phạm Hồng Đức', 'Pham Hong Duc'",
        "'Lê Thành Đạt', 'Le Thanh Dat'",
        "'áàảãạăắằẳẵặâấầẩẫậ', 'aaaaaaaaaaaaaaaaa'",
        "'éèẻẽẹêếềểễệ', 'eeeeeeeeeee'",
        "'íìỉĩị', 'iiiii'",
        "'óòỏõọôốồổỗộơớờởỡợ', 'ooooooooooooooooo'",
        "'úùủũụưứừửữự', 'uuuuuuuuuuu'",
        "'ýỳỷỹỵ', 'yyyyy'"
    })
    @DisplayName("normalizeVietnamese - loại bỏ dấu tiếng Việt")
    void normalizeVietnamese_shouldRemoveVietnameseDiacritics(String input, String expected) {
        assertEquals(expected, StringHelper.normalizeVietnamese(input));
    }

    @ParameterizedTest
    @NullAndEmptySource
    @DisplayName("normalizeVietnamese - xử lý input null và rỗng")
    void normalizeVietnamese_shouldHandleNullAndEmptyInput(String input) {
        assertEquals("", StringHelper.normalizeVietnamese(input));
    }

    @ParameterizedTest
    @CsvSource({
        "'', ''",
        "'Hello', 'hello'",
        "'HELLO', 'hELLO'",
        "'CamelCase', 'camelCase'",
        "'XMLHttpRequest', 'xMLHttpRequest'",
        "'A', 'a'"
    })
    @DisplayName("toCamelCase - chuyển ký tự đầu tiên thành chữ thường")
    void toCamelCase_shouldConvertFirstCharacterToLowercase(String input, String expected) {
        assertEquals(expected, StringHelper.toCamelCase(input));
    }

    @Test
    @DisplayName("toCamelCase - xử lý input null và rỗng")
    void toCamelCase_shouldHandleNullAndEmptyInput() {
        assertNull(StringHelper.toCamelCase(null));
        assertEquals("", StringHelper.toCamelCase(""));
        assertEquals("   ", StringHelper.toCamelCase("   "));
    }

    @ParameterizedTest
    @CsvSource({
        "'', ''",
        "'camelCase', 'camel_case'",
        "'PascalCase', 'pascal_case'",
        "'XMLHttpRequest', 'xml_http_request'",
        "'kebab-case', 'kebab_case'",
        "'space separated', 'space_separated'",
        "'mixed-case_and spaces', 'mixed_case_and_spaces'",
        "'UPPER_CASE', 'upper_case'",
        "'simpleword', 'simpleword'",
        "'   trim spaces   ', 'trim_spaces'"
    })
    @DisplayName("toSnakeCase - chuyển đổi các format khác nhau sang snake_case")
    void toSnakeCase_shouldConvertVariousFormatsToSnakeCase(String input, String expected) {
        assertEquals(expected, StringHelper.toSnakeCase(input));
    }

    @ParameterizedTest
    @NullAndEmptySource
    @DisplayName("toSnakeCase - xử lý input null và rỗng")
    void toSnakeCase_shouldHandleNullAndEmptyInput(String input) {
        assertEquals("", StringHelper.toSnakeCase(input));
    }

    @ParameterizedTest
    @CsvSource({
        "'hello', 'Hello'",
        "'HELLO', 'HELLO'",
        "'hELLO', 'HELLO'",
        "'a', 'A'",
        "'', ''"
    })
    @DisplayName("capitalize - viết hoa ký tự đầu tiên")
    void capitalize_shouldCapitalizeFirstCharacter(String input, String expected) {
        assertEquals(expected, StringHelper.capitalize(input));
    }

    @Test
    @DisplayName("capitalize - xử lý input null")
    void capitalize_shouldHandleNullInput() {
        assertNull(StringHelper.capitalize(null));
    }

    @ParameterizedTest
    @CsvSource({
        "'<p>Hello World</p>', 'Hello World'",
        "'<div><span>Test</span></div>', 'Test'",
        "'<h1>Title</h1><p>Content</p>', 'Title Content'",
        "'<script>alert(\"xss\")</script>Hello', 'Hello'",
        "'Plain text', 'Plain text'",
        "'<p>Line 1</p><p>Line 2</p>', 'Line 1 Line 2'"
    })
    @DisplayName("stripHtml - loại bỏ tất cả thẻ HTML")
    void stripHtml_shouldRemoveAllHtmlTags(String input, String expected) {
        assertEquals(expected, StringHelper.stripHtml(input));
    }

    @ParameterizedTest
    @NullAndEmptySource
    @DisplayName("stripHtml - xử lý input null và rỗng")
    void stripHtml_shouldHandleNullAndEmptyInput(String input) {
        assertEquals("", StringHelper.stripHtml(input));
    }

    @Test
    @DisplayName("sanitizeHtml - giữ lại các thẻ HTML an toàn")
    void sanitizeHtml_shouldKeepSafeHtmlTags() {
        assertEquals("<p>Hello <strong>World</strong></p>", 
                    StringHelper.sanitizeHtml("<p>Hello <strong>World</strong></p>"));
        
        assertEquals("<a href=\"http://example.com\" rel=\"nofollow\">Link</a>", 
                    StringHelper.sanitizeHtml("<a href=\"http://example.com\">Link</a>"));
        
        assertEquals("Hello World", 
                    StringHelper.sanitizeHtml("<script>alert('xss')</script>Hello World"));
        
        assertEquals("<ul><li>Item 1</li><li>Item 2</li></ul>", 
                    StringHelper.sanitizeHtml("<ul><li>Item 1</li><li>Item 2</li></ul>"));
    }

    @ParameterizedTest
    @NullAndEmptySource
    @DisplayName("sanitizeHtml - xử lý input null và rỗng")
    void sanitizeHtml_shouldHandleNullAndEmptyInput(String input) {
        assertEquals("", StringHelper.sanitizeHtml(input));
    }

    @Test
    @DisplayName("hằng số EMPTY - phải là chuỗi rỗng")
    void emptyConstant_shouldBeEmptyString() {
        assertEquals("", StringHelper.EMPTY);
        assertNotNull(StringHelper.EMPTY);
    }
}