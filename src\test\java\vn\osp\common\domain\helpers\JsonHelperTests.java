package vn.osp.common.domain.helpers;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.io.TempDir;

import java.io.File;
import java.io.IOException;
import java.nio.file.Path;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;

@DisplayName("JsonHelper Tests")
class JsonHelperTests {

    // Test data classes
    public static class Person {
        private String name;
        private Integer age;
        private LocalDateTime birthDate;

        public Person() {}

        public Person(String name, Integer age, LocalDateTime birthDate) {
            this.name = name;
            this.age = age;
            this.birthDate = birthDate;
        }

        // Getters and setters
        public String getName() { return name; }
        public void setName(String name) { this.name = name; }
        public Integer getAge() { return age; }
        public void setAge(Integer age) { this.age = age; }
        public LocalDateTime getBirthDate() { return birthDate; }
        public void setBirthDate(LocalDateTime birthDate) { this.birthDate = birthDate; }

        @Override
        public boolean equals(Object obj) {
            if (this == obj) return true;
            if (obj == null || getClass() != obj.getClass()) return false;
            Person person = (Person) obj;
            return java.util.Objects.equals(name, person.name) &&
                   java.util.Objects.equals(age, person.age) &&
                   java.util.Objects.equals(birthDate, person.birthDate);
        }

        @Override
        public int hashCode() {
            return java.util.Objects.hash(name, age, birthDate);
        }
    }

    @Test
    @DisplayName("toJson - Chuyển đổi object thành JSON string thành công")
    void toJson_WithValidObject_ShouldReturnJsonString() throws JsonProcessingException {
        // Given
        Person person = new Person("John Doe", 30, LocalDateTime.of(1993, 5, 15, 10, 30));

        // When
        String json = JsonHelper.toJson(person);

        // Then
        assertNotNull(json);
        assertTrue(json.contains("\"name\" : \"John Doe\""));
        assertTrue(json.contains("\"age\" : 30"));
    }

    @Test
    @DisplayName("toJson - Với null object trả về chuỗi rỗng")
    void toJson_WithNullObject_ShouldReturnEmptyString() throws JsonProcessingException {
        // When
        String json = JsonHelper.toJson(null);

        // Then
        assertEquals(StringHelper.EMPTY, json);
    }

    @Test
    @DisplayName("fromJson - Chuyển đổi JSON string thành object thành công")
    void fromJson_WithValidJson_ShouldReturnObject() throws JsonProcessingException {
        // Given
        String json = "{\"name\":\"Jane Doe\",\"age\":25}";

        // When
        Person person = JsonHelper.fromJson(json, Person.class);

        // Then
        assertNotNull(person);
        assertEquals("Jane Doe", person.getName());
        assertEquals(25, person.getAge());
    }

    @Test
    @DisplayName("fromJson - Với JSON null hoặc rỗng trả về null")
    void fromJson_WithNullOrEmptyJson_ShouldReturnNull() throws JsonProcessingException {
        // When & Then
        assertNull(JsonHelper.fromJson(null, Person.class));
        assertNull(JsonHelper.fromJson("", Person.class));
        assertNull(JsonHelper.fromJson("   ", Person.class));
    }

    @Test
    @DisplayName("fromJson - Với JSON không hợp lệ ném JsonProcessingException")
    void fromJson_WithInvalidJson_ShouldThrowException() {
        // Given
        String invalidJson = "{invalid json}";

        // When & Then
        assertThrows(JsonProcessingException.class, () -> 
            JsonHelper.fromJson(invalidJson, Person.class));
    }

    @Test
    @DisplayName("fromJson với TypeReference - Chuyển đổi JSON thành generic type thành công")
    void fromJson_WithTypeReference_ShouldReturnGenericType() throws JsonProcessingException {
        // Given
        String json = "[{\"name\":\"John\",\"age\":30},{\"name\":\"Jane\",\"age\":25}]";
        TypeReference<List<Person>> typeRef = new TypeReference<List<Person>>() {};

        // When
        List<Person> persons = JsonHelper.fromJson(json, typeRef);

        // Then
        assertNotNull(persons);
        assertEquals(2, persons.size());
        assertEquals("John", persons.get(0).getName());
        assertEquals("Jane", persons.get(1).getName());
    }

    @Test
    @DisplayName("fromJson với TypeReference - Với JSON null trả về null")
    void fromJson_WithTypeReferenceAndNullJson_ShouldReturnNull() throws JsonProcessingException {
        // Given
        TypeReference<List<Person>> typeRef = new TypeReference<List<Person>>() {};

        // When & Then
        assertNull(JsonHelper.fromJson(null, typeRef));
        assertNull(JsonHelper.fromJson("", typeRef));
    }

    @Test
    @DisplayName("deserialize từ byte array - Chuyển đổi thành công")
    void deserialize_FromByteArray_ShouldReturnObject() throws IOException {
        // Given
        Person originalPerson = new Person("Bob", 35, LocalDateTime.now());
        byte[] bytes = JsonHelper.serialize(originalPerson);

        // When
        Person deserializedPerson = JsonHelper.deserialize(bytes, Person.class);

        // Then
        assertNotNull(deserializedPerson);
        assertEquals(originalPerson.getName(), deserializedPerson.getName());
        assertEquals(originalPerson.getAge(), deserializedPerson.getAge());
    }

    @Test
    @DisplayName("deserialize từ byte array - Với null hoặc rỗng trả về null")
    void deserialize_FromNullOrEmptyByteArray_ShouldReturnNull() throws IOException {
        // When & Then
        assertNull(JsonHelper.deserialize((byte[]) null, Person.class));
        assertNull(JsonHelper.deserialize(new byte[0], Person.class));
    }

    @Test
    @DisplayName("deserialize từ byte array với TypeReference - Chuyển đổi thành công")
    void deserialize_FromByteArrayWithTypeReference_ShouldReturnGenericType() throws IOException {
        // Given
        List<Person> originalList = List.of(
            new Person("Alice", 28, LocalDateTime.now()),
            new Person("Charlie", 32, LocalDateTime.now())
        );
        byte[] bytes = JsonHelper.serialize(originalList);
        TypeReference<List<Person>> typeRef = new TypeReference<List<Person>>() {};

        // When
        List<Person> deserializedList = JsonHelper.deserialize(bytes, typeRef);

        // Then
        assertNotNull(deserializedList);
        assertEquals(2, deserializedList.size());
        assertEquals("Alice", deserializedList.get(0).getName());
        assertEquals("Charlie", deserializedList.get(1).getName());
    }

    @Test
    @DisplayName("serialize - Chuyển đổi object thành byte array thành công")
    void serialize_WithValidObject_ShouldReturnByteArray() throws JsonProcessingException {
        // Given
        Person person = new Person("David", 40, LocalDateTime.now());

        // When
        byte[] bytes = JsonHelper.serialize(person);

        // Then
        assertNotNull(bytes);
        assertTrue(bytes.length > 0);
    }

    @Test
    @DisplayName("serialize - Với null object trả về byte array rỗng")
    void serialize_WithNullObject_ShouldReturnEmptyByteArray() throws JsonProcessingException {
        // When
        byte[] bytes = JsonHelper.serialize(null);

        // Then
        assertNotNull(bytes);
        assertEquals(0, bytes.length);
    }

    @Test
    @DisplayName("serialize vào file - Ghi file thành công")
    void serialize_ToFile_ShouldWriteFileSuccessfully(@TempDir Path tempDir) throws IOException {
        // Given
        Person person = new Person("Eve", 27, LocalDateTime.now());
        String filePath = tempDir.resolve("test-person.json").toString();

        // When
        JsonHelper.serialize(person, filePath);

        // Then
        File file = new File(filePath);
        assertTrue(file.exists());
        assertTrue(file.length() > 0);
    }

    @Test
    @DisplayName("serialize vào file - Với null object không ghi file")
    void serialize_ToFileWithNullObject_ShouldNotWriteFile(@TempDir Path tempDir) throws IOException {
        // Given
        String filePath = tempDir.resolve("test-null.json").toString();

        // When
        JsonHelper.serialize(null, filePath);

        // Then
        File file = new File(filePath);
        assertFalse(file.exists());
    }

    @Test
    @DisplayName("deserialize từ file - Đọc file thành công")
    void deserialize_FromFile_ShouldReadFileSuccessfully(@TempDir Path tempDir) throws IOException {
        // Given
        Person originalPerson = new Person("Frank", 45, LocalDateTime.now());
        String filePath = tempDir.resolve("test-deserialize.json").toString();
        JsonHelper.serialize(originalPerson, filePath);

        // When
        Person deserializedPerson = JsonHelper.deserialize(filePath, Person.class);

        // Then
        assertNotNull(deserializedPerson);
        assertEquals(originalPerson.getName(), deserializedPerson.getName());
        assertEquals(originalPerson.getAge(), deserializedPerson.getAge());
    }

    @Test
    @DisplayName("deserialize từ file - Với file không tồn tại trả về null")
    void deserialize_FromNonExistentFile_ShouldReturnNull() throws IOException {
        // Given
        String nonExistentFilePath = "/path/to/non/existent/file.json";

        // When
        Person person = JsonHelper.deserialize(nonExistentFilePath, Person.class);

        // Then
        assertNull(person);
    }

    @Test
    @DisplayName("deserialize từ file với TypeReference - Đọc file thành công")
    void deserialize_FromFileWithTypeReference_ShouldReadFileSuccessfully(@TempDir Path tempDir) throws IOException {
        // Given
        Map<String, Object> originalData = Map.of(
            "name", "Grace",
            "age", 30,
            "active", true
        );
        String filePath = tempDir.resolve("test-map.json").toString();
        JsonHelper.serialize(originalData, filePath);
        TypeReference<Map<String, Object>> typeRef = new TypeReference<Map<String, Object>>() {};

        // When
        Map<String, Object> deserializedData = JsonHelper.deserialize(filePath, typeRef);

        // Then
        assertNotNull(deserializedData);
        assertEquals("Grace", deserializedData.get("name"));
        assertEquals(30, deserializedData.get("age"));
        assertEquals(true, deserializedData.get("active"));
    }

    @Test
    @DisplayName("deserialize từ file với TypeReference - Với file không tồn tại trả về null")
    void deserialize_FromNonExistentFileWithTypeReference_ShouldReturnNull() throws IOException {
        // Given
        String nonExistentFilePath = "/path/to/non/existent/file.json";
        TypeReference<Map<String, Object>> typeRef = new TypeReference<Map<String, Object>>() {};

        // When
        Map<String, Object> data = JsonHelper.deserialize(nonExistentFilePath, typeRef);

        // Then
        assertNull(data);
    }

    @Test
    @DisplayName("Integration test - Toàn bộ quy trình serialize/deserialize")
    void integrationTest_FullSerializeDeserializeCycle() throws IOException {
        // Given
        Person originalPerson = new Person("Integration Test", 99, LocalDateTime.of(2000, 1, 1, 12, 0));

        // When - JSON string conversion
        String json = JsonHelper.toJson(originalPerson);
        Person fromJsonPerson = JsonHelper.fromJson(json, Person.class);

        // When - Byte array conversion
        byte[] bytes = JsonHelper.serialize(originalPerson);
        Person fromBytesPerson = JsonHelper.deserialize(bytes, Person.class);

        // Then
        assertEquals(originalPerson, fromJsonPerson);
        assertEquals(originalPerson, fromBytesPerson);
        assertEquals(fromJsonPerson, fromBytesPerson);
    }

    @Test
    @DisplayName("Test với complex object có nested properties")
    void test_WithComplexNestedObject() throws JsonProcessingException {
        // Given
        Map<String, Object> complexObject = Map.of(
            "user", Map.of("name", "Test User", "email", "<EMAIL>"),
            "settings", Map.of("theme", "dark", "notifications", true),
            "items", List.of("item1", "item2", "item3")
        );

        // When
        String json = JsonHelper.toJson(complexObject);
        TypeReference<Map<String, Object>> typeRef = new TypeReference<Map<String, Object>>() {};
        Map<String, Object> deserializedObject = JsonHelper.fromJson(json, typeRef);

        // Then
        assertNotNull(deserializedObject);
        assertTrue(deserializedObject.containsKey("user"));
        assertTrue(deserializedObject.containsKey("settings"));
        assertTrue(deserializedObject.containsKey("items"));
    }
}