package vn.osp.common.infrastructure.listeners;

import org.springframework.boot.autoconfigure.orm.jpa.HibernatePropertiesCustomizer;

import java.util.Map;

@Component
public class HibernateFilterConfigurer implements HibernatePropertiesCustomizer {

    public static final String SOFT_DELETE_FILTER = "softDeleteFilter";
    public static final String TENANT_FILTER = "tenantFilter";
    public static final String TENANT_ID_PARAM = "tenantId";

    @Override
    public void customize(Map<String, Object> hibernateProperties) {
        hibernateProperties.put("hibernate.session_factory.interceptor", entityInterceptor());
    }

    // Phương pháp chính để tùy chỉnh cấu hình Hibernate
    @Override
    public void customize(Map<String, Object> hibernateProperties, EntityManagerFactoryBuilder builder) {
        // Lấy danh sách tất cả các entity được quản lý
        if (builder instanceof EntityManagerFactoryBuilderImpl) {
            EntityManagerFactoryBuilderImpl builderImpl = (EntityManagerFactoryBuilderImpl) builder;
            ParsedPersistenceXmlDescriptor descriptor = builderImpl.getPersistenceUnit();

            descriptor.getManagedClassNames().stream()
                    .map(this::getClassFromName)
                    .filter(java.util.Objects::nonNull)
                    .forEach(clazz -> {
                        // Nếu entity có khả năng xóa mềm, thêm filter soft-delete
                        if (SoftDeletable.class.isAssignableFrom(clazz)) {
                            builderImpl.addFilterDefinition(
                                    new org.hibernate.engine.spi.FilterDefinition(
                                            SOFT_DELETE_FILTER,
                                            "deleted = false", // Điều kiện mặc định
                                            null // Không có tham số
                                    )
                            );
                        }
                        // Nếu entity là của một tenant, thêm filter tenant
                        if (TenantAware.class.isAssignableFrom(clazz)) {
                            builderImpl.addFilterDefinition(
                                    new org.hibernate.engine.spi.FilterDefinition(
                                            TENANT_FILTER,
                                            "tenant_id = :" + TENANT_ID_PARAM, // Điều kiện với tham số
                                            java.util.Map.of(TENANT_ID_PARAM, StandardBasicTypes.STRING)
                                    )
                            );
                        }
                    });
        }
    }

    private Class<?> getClassFromName(String className) {
        try {
            return Class.forName(className);
        } catch (ClassNotFoundException e) {
            // Xử lý lỗi nếu cần
            return null;
        }
    }
}