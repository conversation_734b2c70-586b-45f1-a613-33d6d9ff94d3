package vn.osp.common.domain.helpers;

import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;

import java.util.*;

import static org.junit.jupiter.api.Assertions.*;

@DisplayName("Kiểm thử CollectionHelper")
class CollectionHelperTests {

    @Nested
    @DisplayName("Kiểm thử Collection isEmpty")
    class CollectionIsEmptyTests {

        @Test
        @DisplayName("Nên trả về true khi collection là null")
        void shouldReturnTrueWhenCollectionIsNull() {
            // Chuẩn bị
            Collection<String> collection = null;

            // Thực hiện
            boolean result = CollectionHelper.isEmpty(collection);

            // Kiểm tra
            assertTrue(result);
        }

        @Test
        @DisplayName("Nên trả về true khi collection rỗng")
        void shouldReturnTrueWhenCollectionIsEmpty() {
            // Chuẩn bị
            Collection<String> collection = new ArrayList<>();

            // Thực hiện
            boolean result = CollectionHelper.isEmpty(collection);

            // Kiểm tra
            assertTrue(result);
        }

        @Test
        @DisplayName("Nên trả về false khi collection có phần tử")
        void shouldReturnFalseWhenCollectionHasElements() {
            // Chuẩn bị
            Collection<String> collection = Arrays.asList("element1", "element2");

            // Thực hiện
            boolean result = CollectionHelper.isEmpty(collection);

            // Kiểm tra
            assertFalse(result);
        }

        @Test
        @DisplayName("Nên trả về false khi collection có một phần tử")
        void shouldReturnFalseWhenCollectionHasSingleElement() {
            // Chuẩn bị
            Collection<String> collection = Collections.singletonList("element");

            // Thực hiện
            boolean result = CollectionHelper.isEmpty(collection);

            // Kiểm tra
            assertFalse(result);
        }

        @Test
        @DisplayName("Nên hoạt động với các loại collection khác nhau")
        void shouldWorkWithDifferentCollectionTypes() {
            // Chuẩn bị
            List<String> list = new ArrayList<>();
            Set<String> set = new HashSet<>();
            Queue<String> queue = new LinkedList<>();

            // Thực hiện & Kiểm tra
            assertTrue(CollectionHelper.isEmpty(list));
            assertTrue(CollectionHelper.isEmpty(set));
            assertTrue(CollectionHelper.isEmpty(queue));

            // Thêm phần tử
            list.add("item");
            set.add("item");
            queue.add("item");

            assertFalse(CollectionHelper.isEmpty(list));
            assertFalse(CollectionHelper.isEmpty(set));
            assertFalse(CollectionHelper.isEmpty(queue));
        }
    }

    @Nested
    @DisplayName("Kiểm thử Collection isNotEmpty")
    class CollectionIsNotEmptyTests {

        @Test
        @DisplayName("Nên trả về false khi collection là null")
        void shouldReturnFalseWhenCollectionIsNull() {
            // Chuẩn bị
            Collection<String> collection = null;

            // Thực hiện
            boolean result = CollectionHelper.isNotEmpty(collection);

            // Kiểm tra
            assertFalse(result);
        }

        @Test
        @DisplayName("Nên trả về false khi collection rỗng")
        void shouldReturnFalseWhenCollectionIsEmpty() {
            // Chuẩn bị
            Collection<String> collection = new ArrayList<>();

            // Thực hiện
            boolean result = CollectionHelper.isNotEmpty(collection);

            // Kiểm tra
            assertFalse(result);
        }

        @Test
        @DisplayName("Nên trả về true khi collection có phần tử")
        void shouldReturnTrueWhenCollectionHasElements() {
            // Chuẩn bị
            Collection<String> collection = Arrays.asList("element1", "element2");

            // Thực hiện
            boolean result = CollectionHelper.isNotEmpty(collection);

            // Kiểm tra
            assertTrue(result);
        }

        @Test
        @DisplayName("Nên trả về true khi collection có một phần tử")
        void shouldReturnTrueWhenCollectionHasSingleElement() {
            // Chuẩn bị
            Collection<String> collection = Collections.singletonList("element");

            // Thực hiện
            boolean result = CollectionHelper.isNotEmpty(collection);

            // Kiểm tra
            assertTrue(result);
        }

        @Test
        @DisplayName("Nên hoạt động với các loại collection khác nhau")
        void shouldWorkWithDifferentCollectionTypes() {
            // Chuẩn bị
            List<String> list = Arrays.asList("item");
            Set<String> set = new HashSet<>(Arrays.asList("item"));
            Queue<String> queue = new LinkedList<>(Arrays.asList("item"));

            // Thực hiện & Kiểm tra
            assertTrue(CollectionHelper.isNotEmpty(list));
            assertTrue(CollectionHelper.isNotEmpty(set));
            assertTrue(CollectionHelper.isNotEmpty(queue));
        }
    }

    @Nested
    @DisplayName("Kiểm thử Map isEmpty")
    class MapIsEmptyTests {

        @Test
        @DisplayName("Nên trả về true khi map là null")
        void shouldReturnTrueWhenMapIsNull() {
            // Chuẩn bị
            Map<String, String> map = null;

            // Thực hiện
            boolean result = CollectionHelper.isEmpty(map);

            // Kiểm tra
            assertTrue(result);
        }

        @Test
        @DisplayName("Nên trả về true khi map rỗng")
        void shouldReturnTrueWhenMapIsEmpty() {
            // Chuẩn bị
            Map<String, String> map = new HashMap<>();

            // Thực hiện
            boolean result = CollectionHelper.isEmpty(map);

            // Kiểm tra
            assertTrue(result);
        }

        @Test
        @DisplayName("Nên trả về false khi map có entry")
        void shouldReturnFalseWhenMapHasEntries() {
            // Chuẩn bị
            Map<String, String> map = new HashMap<>();
            map.put("key1", "value1");
            map.put("key2", "value2");

            // Thực hiện
            boolean result = CollectionHelper.isEmpty(map);

            // Kiểm tra
            assertFalse(result);
        }

        @Test
        @DisplayName("Nên trả về false khi map có một entry")
        void shouldReturnFalseWhenMapHasSingleEntry() {
            // Chuẩn bị
            Map<String, String> map = Collections.singletonMap("key", "value");

            // Thực hiện
            boolean result = CollectionHelper.isEmpty(map);

            // Kiểm tra
            assertFalse(result);
        }

        @Test
        @DisplayName("Nên hoạt động với các loại map khác nhau")
        void shouldWorkWithDifferentMapTypes() {
            // Chuẩn bị
            Map<String, String> hashMap = new HashMap<>();
            Map<String, String> linkedHashMap = new LinkedHashMap<>();
            Map<String, String> treeMap = new TreeMap<>();

            // Thực hiện & Kiểm tra
            assertTrue(CollectionHelper.isEmpty(hashMap));
            assertTrue(CollectionHelper.isEmpty(linkedHashMap));
            assertTrue(CollectionHelper.isEmpty(treeMap));

            // Thêm entry
            hashMap.put("key", "value");
            linkedHashMap.put("key", "value");
            treeMap.put("key", "value");

            assertFalse(CollectionHelper.isEmpty(hashMap));
            assertFalse(CollectionHelper.isEmpty(linkedHashMap));
            assertFalse(CollectionHelper.isEmpty(treeMap));
        }
    }

    @Nested
    @DisplayName("Kiểm thử Map isNotEmpty")
    class MapIsNotEmptyTests {

        @Test
        @DisplayName("Nên trả về false khi map là null")
        void shouldReturnFalseWhenMapIsNull() {
            // Chuẩn bị
            Map<String, String> map = null;

            // Thực hiện
            boolean result = CollectionHelper.isNotEmpty(map);

            // Kiểm tra
            assertFalse(result);
        }

        @Test
        @DisplayName("Nên trả về false khi map rỗng")
        void shouldReturnFalseWhenMapIsEmpty() {
            // Chuẩn bị
            Map<String, String> map = new HashMap<>();

            // Thực hiện
            boolean result = CollectionHelper.isNotEmpty(map);

            // Kiểm tra
            assertFalse(result);
        }

        @Test
        @DisplayName("Nên trả về true khi map có entry")
        void shouldReturnTrueWhenMapHasEntries() {
            // Chuẩn bị
            Map<String, String> map = new HashMap<>();
            map.put("key1", "value1");
            map.put("key2", "value2");

            // Thực hiện
            boolean result = CollectionHelper.isNotEmpty(map);

            // Kiểm tra
            assertTrue(result);
        }

        @Test
        @DisplayName("Nên trả về true khi map có một entry")
        void shouldReturnTrueWhenMapHasSingleEntry() {
            // Chuẩn bị
            Map<String, String> map = Collections.singletonMap("key", "value");

            // Thực hiện
            boolean result = CollectionHelper.isNotEmpty(map);

            // Kiểm tra
            assertTrue(result);
        }

        @Test
        @DisplayName("Nên hoạt động với các loại map khác nhau")
        void shouldWorkWithDifferentMapTypes() {
            // Chuẩn bị
            Map<String, String> hashMap = new HashMap<>();
            hashMap.put("key", "value");
            
            Map<String, String> linkedHashMap = new LinkedHashMap<>();
            linkedHashMap.put("key", "value");
            
            Map<String, String> treeMap = new TreeMap<>();
            treeMap.put("key", "value");

            // Thực hiện & Kiểm tra
            assertTrue(CollectionHelper.isNotEmpty(hashMap));
            assertTrue(CollectionHelper.isNotEmpty(linkedHashMap));
            assertTrue(CollectionHelper.isNotEmpty(treeMap));
        }
    }

    @Nested
    @DisplayName("Kiểm thử các trường hợp đặc biệt")
    class EdgeCasesTests {

        @Test
        @DisplayName("Nên xử lý đúng với mixed types")
        void shouldHandleMixedTypesCorrectly() {
            // Chuẩn bị
            Collection<Object> mixedCollection = Arrays.asList("string", 123, true, null);
            Map<Object, Object> mixedMap = new HashMap<>();
            mixedMap.put("stringKey", 123);
            mixedMap.put(456, "stringValue");
            mixedMap.put(null, null);

            // Thực hiện & Kiểm tra
            assertFalse(CollectionHelper.isEmpty(mixedCollection));
            assertTrue(CollectionHelper.isNotEmpty(mixedCollection));
            
            assertFalse(CollectionHelper.isEmpty(mixedMap));
            assertTrue(CollectionHelper.isNotEmpty(mixedMap));
        }

        @Test
        @DisplayName("Nên xử lý collection chứa phần tử null")
        void shouldHandleCollectionWithNullElements() {
            // Chuẩn bị
            Collection<String> collectionWithNulls = Arrays.asList(null, null, null);

            // Thực hiện & Kiểm tra
            assertFalse(CollectionHelper.isEmpty(collectionWithNulls));
            assertTrue(CollectionHelper.isNotEmpty(collectionWithNulls));
        }

        @Test
        @DisplayName("Nên xử lý map với key và value null")
        void shouldHandleMapWithNullKeysAndValues() {
            // Chuẩn bị
            Map<String, String> mapWithNulls = new HashMap<>();
            mapWithNulls.put(null, "value");
            mapWithNulls.put("key", null);
            mapWithNulls.put(null, null);

            // Thực hiện & Kiểm tra
            assertFalse(CollectionHelper.isEmpty(mapWithNulls));
            assertTrue(CollectionHelper.isNotEmpty(mapWithNulls));
        }

        @Test
        @DisplayName("Các phương thức Collection và Map nên nhất quán")
        void collectionAndMapMethodsShouldBeConsistent() {
            // Chuẩn bị
            Collection<String> emptyCollection = new ArrayList<>();
            Collection<String> nonEmptyCollection = Arrays.asList("item");
            Map<String, String> emptyMap = new HashMap<>();
            Map<String, String> nonEmptyMap = Collections.singletonMap("key", "value");

            // Thực hiện & Kiểm tra - isEmpty và isNotEmpty nên ngược nhau
            assertEquals(CollectionHelper.isEmpty(emptyCollection), !CollectionHelper.isNotEmpty(emptyCollection));
            assertEquals(CollectionHelper.isEmpty(nonEmptyCollection), !CollectionHelper.isNotEmpty(nonEmptyCollection));
            assertEquals(CollectionHelper.isEmpty(emptyMap), !CollectionHelper.isNotEmpty(emptyMap));
            assertEquals(CollectionHelper.isEmpty(nonEmptyMap), !CollectionHelper.isNotEmpty(nonEmptyMap));
        }
    }
}
