package vn.osp.common.domain.domainentitytypes;

import jakarta.annotation.Nullable;

import java.io.Serializable;

/**
 * Model có modified by (người sửa) và thông tin người sửa.
 *
 * @param <TKey>  Kiểu dữ liệu của Id
 * @param <TUser> Kiểu dữ liệu của người sửa
 */
public interface HasModifiedByWithUser<TKey extends Serializable, TUser> extends HasModifiedBy<TKey> {

    /**
     * Thông tin người sửa.
     */
    @Nullable
    TUser getModifiedByUser();
    void setModifiedByUser(@Nullable TUser modifiedByUser);
}