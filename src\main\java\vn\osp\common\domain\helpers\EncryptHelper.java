package vn.osp.common.domain.helpers;

import org.springframework.security.crypto.argon2.Argon2PasswordEncoder;
import org.springframework.security.crypto.bcrypt.BCrypt;
import org.springframework.security.crypto.scrypt.SCryptPasswordEncoder;

import javax.crypto.Cipher;
import javax.crypto.KeyGenerator;
import javax.crypto.Mac;
import javax.crypto.SecretKey;
import javax.crypto.spec.GCMParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Path;
import java.security.*;
import java.security.cert.CertificateException;
import java.security.cert.CertificateFactory;
import java.security.cert.X509Certificate;
import java.security.spec.PKCS8EncodedKeySpec;
import java.security.spec.X509EncodedKeySpec;
import java.util.Base64;

public final class EncryptHelper {

    private EncryptHelper() {
    }

    /* =======================================================
       Hashing (Message Digest)
    ======================================================= */

    /**
     * Tính toán hash SHA-256 cho mảng byte.
     *
     * @param data mảng byte cần hash
     * @return mảng byte chứa SHA-256 hash hoặc mảng rỗng nếu input null/empty
     * @throws NoSuchAlgorithmException nếu thuật toán SHA-256 không được hỗ trợ
     */
    public static byte[] sha256(byte[] data) throws NoSuchAlgorithmException {
        if (data == null || data.length == 0) {
            return new byte[0];
        }
        return MessageDigest.getInstance("SHA-256").digest(data);
    }

    /**
     * Tính toán hash SHA-256 cho chuỗi văn bản và trả về kết quả dạng hex.
     *
     * @param text chuỗi văn bản cần hash
     * @return chuỗi hex chứa SHA-256 hash hoặc chuỗi rỗng nếu input null/empty
     * @throws NoSuchAlgorithmException nếu thuật toán SHA-256 không được hỗ trợ
     */
    public static String sha256Hex(String text) throws NoSuchAlgorithmException {
        if (StringHelper.isEmpty(text)) {
            return StringHelper.EMPTY;
        }
        return EncodingHelper.hexEncode(sha256(text.getBytes(StandardCharsets.UTF_8)));
    }

    /**
     * Tính toán hash SHA-512 cho mảng byte.
     *
     * @param data mảng byte cần hash
     * @return mảng byte chứa SHA-512 hash hoặc mảng rỗng nếu input null/empty
     * @throws NoSuchAlgorithmException nếu thuật toán SHA-512 không được hỗ trợ
     */
    public static byte[] sha512(byte[] data) throws NoSuchAlgorithmException {
        if (data == null || data.length == 0) {
            return new byte[0];
        }
        return MessageDigest.getInstance("SHA-512").digest(data);
    }

    /**
     * Tính toán hash SHA-512 cho chuỗi văn bản và trả về kết quả dạng hex.
     *
     * @param text chuỗi văn bản cần hash
     * @return chuỗi hex chứa SHA-512 hash hoặc chuỗi rỗng nếu input null/empty
     * @throws NoSuchAlgorithmException nếu thuật toán SHA-512 không được hỗ trợ
     */
    public static String sha512Hex(String text) throws NoSuchAlgorithmException {
        if (StringHelper.isEmpty(text)) {
            return StringHelper.EMPTY;
        }
        return EncodingHelper.hexEncode(sha512(text.getBytes(StandardCharsets.UTF_8)));
    }

    /**
     * Tính toán hash MD5 cho mảng byte.
     * Lưu ý: MD5 được coi là không an toàn, chỉ nên sử dụng cho mục đích checksum.
     *
     * @param data mảng byte cần hash
     * @return mảng byte chứa MD5 hash hoặc mảng rỗng nếu input null/empty
     * @throws NoSuchAlgorithmException nếu thuật toán MD5 không được hỗ trợ
     */
    public static byte[] md5(byte[] data) throws NoSuchAlgorithmException {
        if (data == null || data.length == 0) {
            return new byte[0];
        }
        return MessageDigest.getInstance("MD5").digest(data);
    }

    /**
     * Tính toán hash MD5 cho chuỗi văn bản và trả về kết quả dạng hex.
     * Lưu ý: MD5 được coi là không an toàn, chỉ nên sử dụng cho mục đích checksum.
     *
     * @param text chuỗi văn bản cần hash
     * @return chuỗi hex chứa MD5 hash hoặc chuỗi rỗng nếu input null/empty
     * @throws NoSuchAlgorithmException nếu thuật toán MD5 không được hỗ trợ
     */
    public static String md5Hex(String text) throws NoSuchAlgorithmException {
        if (StringHelper.isEmpty(text)) {
            return StringHelper.EMPTY;
        }
        return EncodingHelper.hexEncode(md5(text.getBytes(StandardCharsets.UTF_8)));
    }

    /* =======================================================
       HMAC
    ======================================================= */

    /**
     * Tính toán HMAC-SHA256 cho dữ liệu với khóa bí mật.
     *
     * @param key  khóa bí mật dạng byte array
     * @param data dữ liệu cần tính HMAC
     * @return mảng byte chứa HMAC-SHA256
     * @throws GeneralSecurityException nếu có lỗi với thuật toán hoặc khóa
     */
    public static byte[] hmacSha256(byte[] key, byte[] data) throws GeneralSecurityException {
        Mac mac = Mac.getInstance("HmacSHA256");
        mac.init(new SecretKeySpec(key, "HmacSHA256"));
        return mac.doFinal(data);
    }

    /**
     * Tính toán HMAC-SHA256 cho chuỗi văn bản và trả về kết quả dạng hex.
     *
     * @param key  khóa bí mật dạng byte array
     * @param text chuỗi văn bản cần tính HMAC
     * @return chuỗi hex chứa HMAC-SHA256
     * @throws GeneralSecurityException nếu có lỗi với thuật toán hoặc khóa
     */
    public static String hmacSha256Hex(byte[] key, String text) throws GeneralSecurityException {
        return EncodingHelper.hexEncode(hmacSha256(key, text.getBytes(StandardCharsets.UTF_8)));
    }

    /**
     * Tính toán HMAC-SHA512 cho dữ liệu với khóa bí mật.
     *
     * @param key  khóa bí mật dạng byte array
     * @param data dữ liệu cần tính HMAC
     * @return mảng byte chứa HMAC-SHA512
     * @throws GeneralSecurityException nếu có lỗi với thuật toán hoặc khóa
     */
    public static byte[] hmacSha512(byte[] key, byte[] data) throws GeneralSecurityException {
        Mac mac = Mac.getInstance("HmacSHA512");
        mac.init(new SecretKeySpec(key, "HmacSHA512"));
        return mac.doFinal(data);
    }

    /**
     * Tính toán HMAC-SHA512 cho chuỗi văn bản và trả về kết quả dạng hex.
     *
     * @param key  khóa bí mật dạng byte array
     * @param text chuỗi văn bản cần tính HMAC
     * @return chuỗi hex chứa HMAC-SHA512
     * @throws GeneralSecurityException nếu có lỗi với thuật toán hoặc khóa
     */
    public static String hmacSha512Hex(byte[] key, String text) throws GeneralSecurityException {
        return EncodingHelper.hexEncode(hmacSha512(key, text.getBytes(StandardCharsets.UTF_8)));
    }

    /* =======================================================
       AES/GCM (symmetric encryption)
    ======================================================= */
    private static final int GCM_IV_LENGTH = 12; // 96-bit (chuẩn)
    private static final int GCM_TAG_LENGTH = 128; // 128-bit authentication tag

    /**
     * Mã hóa dữ liệu sử dụng AES/GCM với IV được cung cấp.
     *
     * @param key       khóa AES (16, 24 hoặc 32 bytes)
     * @param iv        initialization vector (12 bytes)
     * @param plaintext dữ liệu gốc cần mã hóa
     * @return dữ liệu đã mã hóa bao gồm authentication tag
     * @throws GeneralSecurityException nếu có lỗi trong quá trình mã hóa
     */
    public static byte[] aesEncrypt(byte[] key, byte[] iv, byte[] plaintext) throws GeneralSecurityException {
        if (iv == null || iv.length != GCM_IV_LENGTH) {
            throw new IllegalArgumentException("IV must be 12 bytes for AES/GCM");
        }
        Cipher cipher = Cipher.getInstance("AES/GCM/NoPadding");
        GCMParameterSpec spec = new GCMParameterSpec(GCM_TAG_LENGTH, iv);
        cipher.init(Cipher.ENCRYPT_MODE, new SecretKeySpec(key, "AES"), spec);
        return cipher.doFinal(plaintext);
    }

    /**
     * Giải mã dữ liệu sử dụng AES/GCM với IV được cung cấp.
     *
     * @param key        khóa AES (16, 24 hoặc 32 bytes)
     * @param iv         initialization vector (12 bytes)
     * @param ciphertext dữ liệu đã mã hóa bao gồm authentication tag
     * @return dữ liệu gốc sau khi giải mã
     * @throws GeneralSecurityException nếu có lỗi trong quá trình giải mã hoặc authentication tag không hợp lệ
     */
    public static byte[] aesDecrypt(byte[] key, byte[] iv, byte[] ciphertext) throws GeneralSecurityException {
        if (iv == null || iv.length != GCM_IV_LENGTH) {
            throw new IllegalArgumentException("IV must be 12 bytes for AES/GCM");
        }
        Cipher cipher = Cipher.getInstance("AES/GCM/NoPadding");
        GCMParameterSpec spec = new GCMParameterSpec(GCM_TAG_LENGTH, iv);
        cipher.init(Cipher.DECRYPT_MODE, new SecretKeySpec(key, "AES"), spec);
        return cipher.doFinal(ciphertext);
    }

    /**
     * Mã hóa chuỗi văn bản sử dụng AES/GCM với IV được cung cấp và trả về kết quả dạng Base64.
     *
     * @param key       khóa AES (16, 24 hoặc 32 bytes)
     * @param iv        initialization vector (12 bytes)
     * @param plaintext chuỗi văn bản cần mã hóa
     * @return chuỗi Base64 chứa dữ liệu đã mã hóa
     * @throws GeneralSecurityException nếu có lỗi trong quá trình mã hóa
     */
    public static String aesEncryptToBase64(byte[] key, byte[] iv, String plaintext) throws GeneralSecurityException {
        byte[] encrypted = aesEncrypt(key, iv, plaintext.getBytes(StandardCharsets.UTF_8));
        return EncodingHelper.base64Encode(encrypted);
    }

    /**
     * Giải mã chuỗi Base64 sử dụng AES/GCM với IV được cung cấp.
     *
     * @param key              khóa AES (16, 24 hoặc 32 bytes)
     * @param iv               initialization vector (12 bytes)
     * @param base64Ciphertext chuỗi Base64 chứa dữ liệu đã mã hóa
     * @return chuỗi văn bản gốc sau khi giải mã
     * @throws GeneralSecurityException nếu có lỗi trong quá trình giải mã hoặc authentication tag không hợp lệ
     */
    public static String aesDecryptFromBase64(byte[] key, byte[] iv, String base64Ciphertext) throws GeneralSecurityException {
        byte[] ciphertext = EncodingHelper.base64Decode(base64Ciphertext);
        return new String(aesDecrypt(key, iv, ciphertext), StandardCharsets.UTF_8);
    }

    /**
     * Mã hóa dữ liệu sử dụng AES/GCM với IV được tạo ngẫu nhiên.
     * IV sẽ được ghép vào đầu kết quả mã hóa.
     *
     * @param key       khóa AES (16, 24 hoặc 32 bytes)
     * @param plaintext dữ liệu gốc cần mã hóa
     * @return dữ liệu đã mã hóa với IV ghép ở đầu
     * @throws GeneralSecurityException nếu có lỗi trong quá trình mã hóa hoặc tạo IV
     */
    public static byte[] aesEncrypt(byte[] key, byte[] plaintext) throws GeneralSecurityException {
        byte[] iv = new byte[GCM_IV_LENGTH];
        SecureRandom random = SecureRandom.getInstanceStrong();
        random.nextBytes(iv);

        Cipher cipher = Cipher.getInstance("AES/GCM/NoPadding");
        GCMParameterSpec spec = new GCMParameterSpec(GCM_TAG_LENGTH, iv);
        cipher.init(Cipher.ENCRYPT_MODE, new SecretKeySpec(key, "AES"), spec);
        byte[] ciphertext = cipher.doFinal(plaintext);

        // Ghép IV + ciphertext
        byte[] result = new byte[iv.length + ciphertext.length];
        System.arraycopy(iv, 0, result, 0, iv.length);
        System.arraycopy(ciphertext, 0, result, iv.length, ciphertext.length);
        return result;
    }

    /**
     * Giải mã dữ liệu sử dụng AES/GCM với IV được trích xuất từ đầu dữ liệu.
     *
     * @param key       khóa AES (16, 24 hoặc 32 bytes)
     * @param encrypted dữ liệu đã mã hóa với IV ghép ở đầu
     * @return dữ liệu gốc sau khi giải mã
     * @throws GeneralSecurityException nếu có lỗi trong quá trình giải mã hoặc authentication tag không hợp lệ
     */
    public static byte[] aesDecrypt(byte[] key, byte[] encrypted) throws GeneralSecurityException {
        if (encrypted == null || encrypted.length <= GCM_IV_LENGTH) {
            throw new IllegalArgumentException("Invalid GCM payload: too short");
        }

        // Tách IV ra
        byte[] iv = new byte[GCM_IV_LENGTH];
        byte[] ciphertext = new byte[encrypted.length - GCM_IV_LENGTH];
        System.arraycopy(encrypted, 0, iv, 0, iv.length);
        System.arraycopy(encrypted, iv.length, ciphertext, 0, ciphertext.length);

        Cipher cipher = Cipher.getInstance("AES/GCM/NoPadding");
        GCMParameterSpec spec = new GCMParameterSpec(GCM_TAG_LENGTH, iv);
        cipher.init(Cipher.DECRYPT_MODE, new SecretKeySpec(key, "AES"), spec);
        return cipher.doFinal(ciphertext);
    }

    /**
     * Mã hóa chuỗi văn bản sử dụng AES/GCM với IV ngẫu nhiên và trả về kết quả dạng Base64.
     *
     * @param key       khóa AES (16, 24 hoặc 32 bytes)
     * @param plaintext chuỗi văn bản cần mã hóa
     * @return chuỗi Base64 chứa dữ liệu đã mã hóa với IV ghép ở đầu
     * @throws GeneralSecurityException nếu có lỗi trong quá trình mã hóa
     */
    public static String aesEncryptToBase64(byte[] key, String plaintext) throws GeneralSecurityException {
        byte[] encrypted = aesEncrypt(key, plaintext.getBytes(StandardCharsets.UTF_8));
        return EncodingHelper.base64Encode(encrypted);
    }

    /**
     * Giải mã chuỗi Base64 từ AES/GCM với IV được trích xuất từ đầu dữ liệu.
     *
     * @param key              khóa AES (16, 24 hoặc 32 bytes)
     * @param base64Ciphertext chuỗi Base64 chứa dữ liệu đã mã hóa với IV ghép ở đầu
     * @return chuỗi văn bản gốc sau khi giải mã
     * @throws GeneralSecurityException nếu có lỗi trong quá trình giải mã hoặc authentication tag không hợp lệ
     */
    public static String aesDecryptFromBase64(byte[] key, String base64Ciphertext) throws GeneralSecurityException {
        byte[] encrypted = EncodingHelper.base64Decode(base64Ciphertext);
        byte[] decrypted = aesDecrypt(key, encrypted);
        return new String(decrypted, StandardCharsets.UTF_8);
    }

    /* =======================================================
       RSA (asymmetric encryption)
    ======================================================= */

    /**
     * Mã hóa dữ liệu bằng thuật toán RSA sử dụng public key.
     * Sử dụng padding OAEP với SHA-256 để đảm bảo an toàn.
     *
     * @param publicKey public key để mã hóa
     * @param data      dữ liệu cần mã hóa
     * @return dữ liệu đã mã hóa
     * @throws GeneralSecurityException nếu có lỗi trong quá trình mã hóa
     */
    public static byte[] rsaEncrypt(PublicKey publicKey, byte[] data) throws GeneralSecurityException {
        Cipher cipher = Cipher.getInstance("RSA/ECB/OAEPWithSHA-256AndMGF1Padding");
        cipher.init(Cipher.ENCRYPT_MODE, publicKey);
        return cipher.doFinal(data);
    }

    /**
     * Giải mã dữ liệu bằng thuật toán RSA sử dụng private key.
     * Sử dụng padding OAEP với SHA-256 để đảm bảo an toàn.
     *
     * @param privateKey private key để giải mã
     * @param data       dữ liệu đã mã hóa
     * @return dữ liệu gốc sau khi giải mã
     * @throws GeneralSecurityException nếu có lỗi trong quá trình giải mã
     */
    public static byte[] rsaDecrypt(PrivateKey privateKey, byte[] data) throws GeneralSecurityException {
        Cipher cipher = Cipher.getInstance("RSA/ECB/OAEPWithSHA-256AndMGF1Padding");
        cipher.init(Cipher.DECRYPT_MODE, privateKey);
        return cipher.doFinal(data);
    }

    /**
     * Mã hóa chuỗi văn bản bằng RSA và trả về kết quả dạng Base64.
     *
     * @param publicKey public key để mã hóa
     * @param plaintext chuỗi văn bản cần mã hóa
     * @return chuỗi Base64 chứa dữ liệu đã mã hóa
     * @throws GeneralSecurityException nếu có lỗi trong quá trình mã hóa
     */
    public static String rsaEncryptToBase64(PublicKey publicKey, String plaintext) throws GeneralSecurityException {
        return EncodingHelper.base64Encode(rsaEncrypt(publicKey, plaintext.getBytes(StandardCharsets.UTF_8)));
    }

    /**
     * Giải mã chuỗi Base64 bằng RSA và trả về văn bản gốc.
     *
     * @param privateKey       private key để giải mã
     * @param base64Ciphertext chuỗi Base64 chứa dữ liệu đã mã hóa
     * @return chuỗi văn bản gốc sau khi giải mã
     * @throws GeneralSecurityException nếu có lỗi trong quá trình giải mã
     */
    public static String rsaDecryptFromBase64(PrivateKey privateKey, String base64Ciphertext) throws GeneralSecurityException {
        byte[] ciphertext = EncodingHelper.base64Decode(base64Ciphertext);
        return new String(rsaDecrypt(privateKey, ciphertext), StandardCharsets.UTF_8);
    }

    /* =======================================================
       BCrypt
    ======================================================= */

    /**
     * Hash mật khẩu sử dụng thuật toán BCrypt với salt ngẫu nhiên.
     * Sử dụng 10 rounds mặc định để cân bằng giữa bảo mật và hiệu năng.
     *
     * @param password mật khẩu gốc cần hash
     * @return chuỗi hash BCrypt bao gồm salt và hash value
     */
    public static String hashPasswordBCrypt(String password) {
        return BCrypt.hashpw(password, BCrypt.gensalt()); // mặc định 10 rounds
    }

    /**
     * Xác thực mật khẩu với hash BCrypt.
     *
     * @param password mật khẩu gốc cần xác thực
     * @param hashed   chuỗi hash BCrypt để so sánh
     * @return true nếu mật khẩu khớp, false nếu không khớp
     */
    public static boolean verifyPasswordBCrypt(String password, String hashed) {
        return BCrypt.checkpw(password, hashed);
    }

    /* =======================================================
       SCrypt
    ======================================================= */
    // N=16384, r=8, p=1 là khuyến nghị OWASP
    private static final SCryptPasswordEncoder scryptEncoder = SCryptPasswordEncoder.defaultsForSpringSecurity_v5_8();

    /**
     * Hash mật khẩu sử dụng thuật toán SCrypt.
     * Sử dụng tham số N=16384, r=8, p=1 theo khuyến nghị OWASP.
     *
     * @param password mật khẩu gốc cần hash
     * @return chuỗi hash SCrypt bao gồm salt và hash value
     */
    public static String hashPasswordSCrypt(String password) {
        return scryptEncoder.encode(password);
    }

    /**
     * Xác thực mật khẩu với hash SCrypt.
     *
     * @param password mật khẩu gốc cần xác thực
     * @param hashed   chuỗi hash SCrypt để so sánh
     * @return true nếu mật khẩu khớp, false nếu không khớp
     */
    public static boolean verifyPasswordSCrypt(String password, String hashed) {
        return scryptEncoder.matches(password, hashed);
    }

    /* =======================================================
       Argon2
    ======================================================= */
    // salt=16 bytes, hash length=32, parallelism=1, memory=4MB, iterations=3
    private static final Argon2PasswordEncoder argon2Encoder = new Argon2PasswordEncoder(16, 32, 1, 1 << 12, 3);

    /**
     * Hash mật khẩu sử dụng thuật toán Argon2 (khuyến nghị hiện tại cho password hashing).
     * Sử dụng tham số: salt=16 bytes, hash length=32, parallelism=1, memory=4MB, iterations=3.
     *
     * @param password mật khẩu gốc cần hash
     * @return chuỗi hash Argon2 bao gồm salt và hash value
     */
    public static String hashPasswordArgon2(String password) {
        return argon2Encoder.encode(password);
    }

    /**
     * Xác thực mật khẩu với hash Argon2.
     *
     * @param password mật khẩu gốc cần xác thực
     * @param hashed   chuỗi hash Argon2 để so sánh
     * @return true nếu mật khẩu khớp, false nếu không khớp
     */
    public static boolean verifyPasswordArgon2(String password, String hashed) {
        return argon2Encoder.matches(password, hashed);
    }

    /* =======================================================
       Key Utilities
    ======================================================= */

    /**
     * Tạo cặp khóa RSA (public/private key pair) với kích thước được chỉ định.
     *
     * @param keySize kích thước khóa tính bằng bit (khuyến nghị: 2048 hoặc 4096)
     * @return cặp khóa RSA mới được tạo
     * @throws GeneralSecurityException nếu có lỗi trong quá trình tạo khóa
     */
    public static KeyPair generateRsaKeyPair(int keySize) throws GeneralSecurityException {
        KeyPairGenerator gen = KeyPairGenerator.getInstance("RSA");
        gen.initialize(keySize);
        return gen.generateKeyPair();
    }

    /**
     * Tạo khóa bí mật AES với kích thước được chỉ định.
     *
     * @param keySize kích thước khóa tính bằng bit (128, 192 hoặc 256)
     * @return khóa AES mới được tạo
     * @throws GeneralSecurityException nếu có lỗi trong quá trình tạo khóa
     */
    public static SecretKey generateAesKey(int keySize) throws GeneralSecurityException {
        KeyGenerator keyGen = KeyGenerator.getInstance("AES");
        keyGen.init(keySize);
        return keyGen.generateKey();
    }

    /**
     * Tải public key từ chuỗi PEM.
     * Tự động loại bỏ header/footer và khoảng trắng.
     *
     * @param pem chuỗi PEM chứa public key
     * @return public key đã được parse
     * @throws GeneralSecurityException nếu có lỗi trong quá trình parse hoặc định dạng PEM không hợp lệ
     */
    public static PublicKey loadPublicKeyFromPem(String pem) throws GeneralSecurityException {
        String content = pem.replaceAll("-----\\w+ PUBLIC KEY-----", "").replaceAll("\\s", "");
        byte[] decoded = Base64.getDecoder().decode(content);
        X509EncodedKeySpec spec = new X509EncodedKeySpec(decoded);
        return KeyFactory.getInstance("RSA").generatePublic(spec);
    }

    /**
     * Tải private key từ chuỗi PEM.
     * Tự động loại bỏ header/footer và khoảng trắng.
     *
     * @param pem chuỗi PEM chứa private key
     * @return private key đã được parse
     * @throws GeneralSecurityException nếu có lỗi trong quá trình parse hoặc định dạng PEM không hợp lệ
     */
    public static PrivateKey loadPrivateKeyFromPem(String pem) throws GeneralSecurityException {
        String content = pem.replaceAll("-----\\w+ PRIVATE KEY-----", "").replaceAll("\\s", "");
        byte[] decoded = Base64.getDecoder().decode(content);
        PKCS8EncodedKeySpec spec = new PKCS8EncodedKeySpec(decoded);
        return KeyFactory.getInstance("RSA").generatePrivate(spec);
    }

    /**
     * Tải public key từ certificate X.509.
     *
     * @param certBytes dữ liệu certificate dạng byte array
     * @return public key được trích xuất từ certificate
     * @throws CertificateException nếu có lỗi trong quá trình parse certificate
     */
    public static PublicKey loadPublicKeyFromCert(byte[] certBytes) throws CertificateException {
        CertificateFactory factory = CertificateFactory.getInstance("X.509");
        X509Certificate cert = (X509Certificate) factory.generateCertificate(new ByteArrayInputStream(certBytes));
        return cert.getPublicKey();
    }

    /**
     * Tải public key từ file PEM.
     *
     * @param pemFile đường dẫn đến file PEM chứa public key
     * @return public key đã được parse
     * @throws IOException              nếu có lỗi đọc file
     * @throws GeneralSecurityException nếu có lỗi trong quá trình parse khóa
     */
    public static PublicKey loadPublicKeyFromPem(Path pemFile) throws IOException, GeneralSecurityException {
        String pem = Files.readString(pemFile);
        return loadPublicKeyFromPem(pem);
    }

    /**
     * Tải private key từ file PEM.
     *
     * @param pemFile đường dẫn đến file PEM chứa private key
     * @return private key đã được parse
     * @throws IOException              nếu có lỗi đọc file
     * @throws GeneralSecurityException nếu có lỗi trong quá trình parse khóa
     */
    public static PrivateKey loadPrivateKeyFromPem(Path pemFile) throws IOException, GeneralSecurityException {
        String pem = Files.readString(pemFile);
        return loadPrivateKeyFromPem(pem);
    }

    /**
     * Tải public key từ file certificate X.509.
     *
     * @param certPath đường dẫn đến file certificate
     * @return public key được trích xuất từ certificate
     * @throws IOException              nếu có lỗi đọc file
     * @throws GeneralSecurityException nếu có lỗi trong quá trình parse certificate
     */
    public static PublicKey loadPublicKeyFromCert(Path certPath) throws IOException, GeneralSecurityException {
        byte[] certBytes = Files.readAllBytes(certPath);
        return loadPublicKeyFromCert(certBytes);
    }
}
