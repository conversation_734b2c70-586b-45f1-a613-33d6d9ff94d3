package vn.osp.common.domain.domainentitytypes;

import jakarta.annotation.Nullable;

import java.io.Serializable;

/**
 * Model có created by (người tạo) và created (thời gian tạo)
 * với thông tin người tạo.
 *
 * @param <TKey>  kiểu dữ liệu khóa chính
 * @param <TUser> kiểu dữ liệu của thông tin người tạo
 */
public interface HasCreatedByWithUser<TKey extends Serializable, TUser> extends HasCreatedBy<TKey> {

    /**
     * Thông tin người tạo
     */
    @Nullable
    TUser getCreatedByUser();
    void setCreatedByUser(@Nullable TUser user);
}