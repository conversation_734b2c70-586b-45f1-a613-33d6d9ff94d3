package vn.osp.common.infrastructure.config;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import vn.osp.common.api.filters.RequestLoggingFilter;

@Configuration
public class RequestLoggingFilterConfig {

    @Value("${request.logging.max-payload-length:1000}")
    private int maxPayloadLength;

    @Bean
    public RequestLoggingFilter logFilter() {
        RequestLoggingFilter filter = new RequestLoggingFilter();
        filter.setIncludeQueryString(true);
        filter.setIncludePayload(true);
        filter.setMaxPayloadLength(maxPayloadLength);
        filter.setIncludeHeaders(false);
        filter.setBeforeMessagePrefix("");
        filter.setAfterMessagePrefix("");
        filter.setBeforeMessageSuffix("");
        filter.setAfterMessageSuffix("");
        return filter;
    }
}
