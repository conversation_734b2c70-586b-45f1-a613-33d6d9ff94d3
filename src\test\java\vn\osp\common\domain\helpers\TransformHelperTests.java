package vn.osp.common.domain.helpers;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import org.junit.jupiter.api.Test;

import java.io.*;
import java.nio.charset.StandardCharsets;
import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;

class TransformHelperTests {

    @Test
    void stringToByteArray_withValidString_shouldReturnByteArray() {
        // Given
        String input = "Hello World";
        
        // When
        byte[] result = TransformHelper.stringToByteArray(input);
        
        // Then
        assertNotNull(result);
        assertArrayEquals(input.getBytes(StandardCharsets.UTF_8), result);
    }

    @Test
    void stringToByteArray_withEmptyString_shouldReturnEmptyArray() {
        // Given
        String input = "";
        
        // When
        byte[] result = TransformHelper.stringToByteArray(input);
        
        // Then
        assertNotNull(result);
        assertEquals(0, result.length);
    }

    @Test
    void stringToByteArray_withNullString_shouldReturnEmptyArray() {
        // Given
        String input = null;
        
        // When
        byte[] result = TransformHelper.stringToByteArray(input);
        
        // Then
        assertNotNull(result);
        assertEquals(0, result.length);
    }

    @Test
    void stringToByteArray_withUnicodeString_shouldHandleCorrectly() {
        // Given
        String input = "Xin chào Việt Nam! 🇻🇳";
        
        // When
        byte[] result = TransformHelper.stringToByteArray(input);
        
        // Then
        assertNotNull(result);
        assertArrayEquals(input.getBytes(StandardCharsets.UTF_8), result);
    }

    @Test
    void byteArrayToString_withValidByteArray_shouldReturnString() {
        // Given
        String original = "Hello World";
        byte[] input = original.getBytes(StandardCharsets.UTF_8);
        
        // When
        String result = TransformHelper.byteArrayToString(input);
        
        // Then
        assertEquals(original, result);
    }

    @Test
    void byteArrayToString_withEmptyByteArray_shouldReturnEmptyString() {
        // Given
        byte[] input = new byte[0];
        
        // When
        String result = TransformHelper.byteArrayToString(input);
        
        // Then
        assertEquals("", result);
    }

    @Test
    void byteArrayToString_withNullByteArray_shouldReturnEmptyString() {
        // Given
        byte[] input = null;
        
        // When
        String result = TransformHelper.byteArrayToString(input);
        
        // Then
        assertEquals("", result);
    }

    @Test
    void byteArrayToString_withUnicodeBytes_shouldHandleCorrectly() {
        // Given
        String original = "Xin chào Việt Nam! 🇻🇳";
        byte[] input = original.getBytes(StandardCharsets.UTF_8);
        
        // When
        String result = TransformHelper.byteArrayToString(input);
        
        // Then
        assertEquals(original, result);
    }

    @Test
    void streamToBase64_withValidInputStream_shouldReturnBase64String() throws IOException {
        // Given
        String original = "Hello World";
        InputStream input = new ByteArrayInputStream(original.getBytes(StandardCharsets.UTF_8));
        
        // When
        String result = TransformHelper.streamToBase64(input);
        
        // Then
        assertNotNull(result);
        assertFalse(result.isEmpty());
        
        // Verify by decoding back
        byte[] decoded = EncodingHelper.base64Decode(result);
        assertEquals(original, new String(decoded, StandardCharsets.UTF_8));
    }

    @Test
    void streamToBase64_withEmptyInputStream_shouldReturnBase64EmptyString() throws IOException {
        // Given
        InputStream input = new ByteArrayInputStream(new byte[0]);
        
        // When
        String result = TransformHelper.streamToBase64(input);
        
        // Then
        assertNotNull(result);
        assertEquals("", result);
    }

    @Test
    void streamToBase64_withNullInputStream_shouldReturnEmptyString() throws IOException {
        // Given
        InputStream input = null;
        
        // When
        String result = TransformHelper.streamToBase64(input);
        
        // Then
        assertEquals("", result);
    }

    @Test
    void streamToByteArray_withValidInputStream_shouldReturnByteArray() throws IOException {
        // Given
        String original = "Hello World";
        InputStream input = new ByteArrayInputStream(original.getBytes(StandardCharsets.UTF_8));
        
        // When
        byte[] result = TransformHelper.streamToByteArray(input);
        
        // Then
        assertNotNull(result);
        assertArrayEquals(original.getBytes(StandardCharsets.UTF_8), result);
    }

    @Test
    void streamToByteArray_withEmptyInputStream_shouldReturnEmptyArray() throws IOException {
        // Given
        InputStream input = new ByteArrayInputStream(new byte[0]);
        
        // When
        byte[] result = TransformHelper.streamToByteArray(input);
        
        // Then
        assertNotNull(result);
        assertEquals(0, result.length);
    }

    @Test
    void streamToByteArray_withNullInputStream_shouldReturnEmptyArray() throws IOException {
        // Given
        InputStream input = null;
        
        // When
        byte[] result = TransformHelper.streamToByteArray(input);
        
        // Then
        assertNotNull(result);
        assertEquals(0, result.length);
    }

    @Test
    void streamToByteArray_withLargeInputStream_shouldHandleCorrectly() throws IOException {
        // Given
        byte[] largeData = new byte[32 * 1024]; // 32KB
        for (int i = 0; i < largeData.length; i++) {
            largeData[i] = (byte) (i % 256);
        }
        InputStream input = new ByteArrayInputStream(largeData);
        
        // When
        byte[] result = TransformHelper.streamToByteArray(input);
        
        // Then
        assertNotNull(result);
        assertArrayEquals(largeData, result);
    }

    @Test
    void base64ToStream_withValidBase64_shouldReturnInputStream() throws IOException {
        // Given
        String original = "Hello World";
        String base64 = EncodingHelper.base64Encode(original);
        
        // When
        InputStream result = TransformHelper.base64ToStream(base64);
        
        // Then
        assertNotNull(result);
        
        byte[] bytes = TransformHelper.streamToByteArray(result);
        String decoded = new String(bytes, StandardCharsets.UTF_8);
        assertEquals(original, decoded);
    }

    @Test
    void base64ToStream_withEmptyBase64_shouldReturnNull() {
        // Given
        String base64 = "";
        
        // When
        InputStream result = TransformHelper.base64ToStream(base64);
        
        // Then
        assertNull(result);
    }

    @Test
    void base64ToStream_withNullBase64_shouldReturnNull() {
        // Given
        String base64 = null;
        
        // When
        InputStream result = TransformHelper.base64ToStream(base64);
        
        // Then
        assertNull(result);
    }

    @Test
    void streamToString_withValidInputStream_shouldReturnString() throws IOException {
        // Given
        String original = "Hello\nWorld\nMultiple\nLines";
        InputStream input = new ByteArrayInputStream(original.getBytes(StandardCharsets.UTF_8));
        
        // When
        String result = TransformHelper.streamToString(input);
        
        // Then
        assertEquals(original, result);
    }

    @Test
    void streamToString_withEmptyInputStream_shouldReturnEmptyString() throws IOException {
        // Given
        InputStream input = new ByteArrayInputStream(new byte[0]);
        
        // When
        String result = TransformHelper.streamToString(input);
        
        // Then
        assertEquals("", result);
    }

    @Test
    void streamToString_withNullInputStream_shouldReturnEmptyString() throws IOException {
        // Given
        InputStream input = null;
        
        // When
        String result = TransformHelper.streamToString(input);
        
        // Then
        assertEquals("", result);
    }

    @Test
    void streamToString_withSingleLineInputStream_shouldReturnStringWithoutNewline() throws IOException {
        // Given
        String original = "Single line without newline";
        InputStream input = new ByteArrayInputStream(original.getBytes(StandardCharsets.UTF_8));
        
        // When
        String result = TransformHelper.streamToString(input);
        
        // Then
        assertEquals(original, result);
    }

    @Test
    void objectToBase64_withValidObject_shouldReturnBase64String() throws JsonProcessingException {
        // Given
        TestObject obj = new TestObject("test", 123);
        
        // When
        String result = TransformHelper.objectToBase64(obj);
        
        // Then
        assertNotNull(result);
        assertFalse(result.isEmpty());
        
        // Verify by decoding back
        String decoded = EncodingHelper.base64DecodeToString(result);
        assertTrue(decoded.contains("test"));
        assertTrue(decoded.contains("123"));
    }

    @Test
    void objectToBase64_withNullObject_shouldReturnEmptyString() throws JsonProcessingException {
        // Given
        Object obj = null;
        
        // When
        String result = TransformHelper.objectToBase64(obj);
        
        // Then
        assertEquals("", result);
    }

    @Test
    void objectToBase64_withComplexObject_shouldHandleCorrectly() throws JsonProcessingException {
        // Given
        Map<String, Object> complexObj = Map.of(
            "name", "test",
            "numbers", List.of(1, 2, 3),
            "nested", Map.of("key", "value")
        );
        
        // When
        String result = TransformHelper.objectToBase64(complexObj);
        
        // Then
        assertNotNull(result);
        assertFalse(result.isEmpty());
    }

    @Test
    void byteArrayToStream_withValidByteArray_shouldReturnInputStream() throws IOException {
        // Given
        String original = "Hello World";
        byte[] input = original.getBytes(StandardCharsets.UTF_8);
        
        // When
        InputStream result = TransformHelper.byteArrayToStream(input);
        
        // Then
        assertNotNull(result);
        
        byte[] readBytes = TransformHelper.streamToByteArray(result);
        assertArrayEquals(input, readBytes);
    }

    @Test
    void byteArrayToStream_withEmptyByteArray_shouldReturnNull() {
        // Given
        byte[] input = new byte[0];
        
        // When
        InputStream result = TransformHelper.byteArrayToStream(input);
        
        // Then
        assertNull(result);
    }

    @Test
    void byteArrayToStream_withNullByteArray_shouldReturnNull() {
        // Given
        byte[] input = null;
        
        // When
        InputStream result = TransformHelper.byteArrayToStream(input);
        
        // Then
        assertNull(result);
    }

    @Test
    void toByteArray_withValidObject_shouldReturnMessagePackBytes() throws JsonProcessingException {
        // Given
        TestObject obj = new TestObject("test", 123);
        
        // When
        byte[] result = TransformHelper.toByteArray(obj);
        
        // Then
        assertNotNull(result);
        assertTrue(result.length > 0);
    }

    @Test
    void toByteArray_withNullObject_shouldReturnEmptyArray() throws JsonProcessingException {
        // Given
        Object obj = null;
        
        // When
        byte[] result = TransformHelper.toByteArray(obj);
        
        // Then
        assertNotNull(result);
        assertEquals(0, result.length);
    }

    @Test
    void fromByteArray_withValidBytesAndClass_shouldReturnObject() throws IOException, JsonProcessingException {
        // Given
        TestObject original = new TestObject("test", 123);
        byte[] bytes = TransformHelper.toByteArray(original);
        
        // When
        TestObject result = TransformHelper.fromByteArray(bytes, TestObject.class);
        
        // Then
        assertNotNull(result);
        assertEquals(original.getName(), result.getName());
        assertEquals(original.getValue(), result.getValue());
    }

    @Test
    void fromByteArray_withEmptyBytesAndClass_shouldReturnNull() throws IOException {
        // Given
        byte[] bytes = new byte[0];
        
        // When
        TestObject result = TransformHelper.fromByteArray(bytes, TestObject.class);
        
        // Then
        assertNull(result);
    }

    @Test
    void fromByteArray_withNullBytesAndClass_shouldReturnNull() throws IOException {
        // Given
        byte[] bytes = null;
        
        // When
        TestObject result = TransformHelper.fromByteArray(bytes, TestObject.class);
        
        // Then
        assertNull(result);
    }

    @Test
    void fromByteArray_withValidBytesAndTypeReference_shouldReturnObject() throws IOException, JsonProcessingException {
        // Given
        List<String> original = List.of("item1", "item2", "item3");
        byte[] bytes = TransformHelper.toByteArray(original);
        TypeReference<List<String>> typeRef = new TypeReference<List<String>>() {};
        
        // When
        List<String> result = TransformHelper.fromByteArray(bytes, typeRef);
        
        // Then
        assertNotNull(result);
        assertEquals(original.size(), result.size());
        assertEquals(original, result);
    }

    @Test
    void fromByteArray_withEmptyBytesAndTypeReference_shouldReturnNull() throws IOException {
        // Given
        byte[] bytes = new byte[0];
        TypeReference<List<String>> typeRef = new TypeReference<List<String>>() {};
        
        // When
        List<String> result = TransformHelper.fromByteArray(bytes, typeRef);
        
        // Then
        assertNull(result);
    }

    @Test
    void fromByteArray_withNullBytesAndTypeReference_shouldReturnNull() throws IOException {
        // Given
        byte[] bytes = null;
        TypeReference<List<String>> typeRef = new TypeReference<List<String>>() {};
        
        // When
        List<String> result = TransformHelper.fromByteArray(bytes, typeRef);
        
        // Then
        assertNull(result);
    }

    @Test
    void roundTripConversion_stringToByteArrayToString_shouldPreserveData() {
        // Given
        String original = "Xin chào Việt Nam! 🇻🇳";
        
        // When
        byte[] bytes = TransformHelper.stringToByteArray(original);
        String result = TransformHelper.byteArrayToString(bytes);
        
        // Then
        assertEquals(original, result);
    }

    @Test
    void roundTripConversion_objectToBase64ToObject_shouldPreserveData() throws JsonProcessingException {
        // Given
        TestObject original = new TestObject("test", 456);
        
        // When
        String base64 = TransformHelper.objectToBase64(original);
        String json = EncodingHelper.base64DecodeToString(base64);
        // Note: We can't directly convert back from base64 to object in TransformHelper
        // since it only provides object->base64, not base64->object
        
        // Then
        assertNotNull(base64);
        assertFalse(base64.isEmpty());
        assertTrue(json.contains("test"));
        assertTrue(json.contains("456"));
    }

    @Test
    void roundTripConversion_streamToByteArrayToStream_shouldPreserveData() throws IOException {
        // Given
        String original = "Hello World Stream Test";
        InputStream originalStream = new ByteArrayInputStream(original.getBytes(StandardCharsets.UTF_8));
        
        // When
        byte[] bytes = TransformHelper.streamToByteArray(originalStream);
        InputStream resultStream = TransformHelper.byteArrayToStream(bytes);
        String result = TransformHelper.streamToString(resultStream);
        
        // Then
        assertEquals(original, result);
    }

    // Test helper class
    public static class TestObject {
        private String name;
        private int value;

        public TestObject() {}

        public TestObject(String name, int value) {
            this.name = name;
            this.value = value;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public int getValue() {
            return value;
        }

        public void setValue(int value) {
            this.value = value;
        }
    }
}